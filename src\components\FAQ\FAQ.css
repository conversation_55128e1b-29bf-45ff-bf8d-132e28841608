/* FAQ Component Styles */
.faq-section {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 4rem 0;
  position: relative;
  overflow: hidden;
}

.faq-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
}

.faq-header {
  text-align: center;
  margin-bottom: 3rem;
}

.faq-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  position: relative;
}

.faq-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 2px;
}

/* FAQ List */
.faq-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.faq-item {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
}

.faq-item:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.faq-item.open {
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.15);
  border-color: #3b82f6;
}

/* FAQ Question Button */
.faq-question {
  width: 100%;
  padding: 1.5rem 2rem;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  transition: all 0.3s ease;
  position: relative;
}

.faq-question:hover {
  background: #f8fafc;
}

.faq-item.open .faq-question {
  background: #f1f5f9;
  color: #1d4ed8;
}

.question-number {
  font-weight: 700;
  color: #3b82f6;
  min-width: 24px;
}

.question-text {
  flex: 1;
  line-height: 1.5;
}

.faq-icon {
  color: #64748b;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #f1f5f9;
}

.faq-item.open .faq-icon {
  color: #1d4ed8;
  background: #dbeafe;
  transform: rotate(180deg);
}

/* FAQ Answer */
.faq-answer {
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: #f8fafc;
}

.answer-content {
  padding: 0 2rem 1.5rem 4rem;
}

.answer-content p {
  margin: 0;
  color: #475569;
  line-height: 1.7;
  font-size: 1rem;
}

/* Decorative Elements */
.faq-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(29, 78, 216, 0.05));
}

.decoration-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  right: -100px;
  animation: float 6s ease-in-out infinite;
}

.decoration-2 {
  width: 150px;
  height: 150px;
  bottom: 20%;
  left: -75px;
  animation: float 8s ease-in-out infinite reverse;
}

.decoration-triangle {
  position: absolute;
  top: 30%;
  right: 5%;
  width: 0;
  height: 0;
  border-left: 30px solid transparent;
  border-right: 30px solid transparent;
  border-bottom: 52px solid rgba(59, 130, 246, 0.08);
  animation: rotate 10s linear infinite;
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .faq-section {
    padding: 3rem 0;
  }
  
  .faq-container {
    padding: 0 1.5rem;
  }
  
  .faq-title {
    font-size: 2.25rem;
  }
}

@media (max-width: 768px) {
  .faq-section {
    padding: 2.5rem 0;
  }
  
  .faq-container {
    padding: 0 1rem;
  }
  
  .faq-title {
    font-size: 2rem;
  }
  
  .faq-question {
    padding: 1.25rem 1.5rem;
    font-size: 1rem;
    gap: 0.75rem;
  }
  
  .answer-content {
    padding: 0 1.5rem 1.25rem 3rem;
  }
  
  .decoration-1 {
    width: 150px;
    height: 150px;
    right: -75px;
  }
  
  .decoration-2 {
    width: 100px;
    height: 100px;
    left: -50px;
  }
  
  .decoration-triangle {
    border-left-width: 20px;
    border-right-width: 20px;
    border-bottom-width: 35px;
  }
}

@media (max-width: 480px) {
  .faq-section {
    padding: 2rem 0;
  }
  
  .faq-container {
    padding: 0 0.75rem;
  }
  
  .faq-title {
    font-size: 1.75rem;
  }
  
  .faq-header {
    margin-bottom: 2rem;
  }
  
  .faq-question {
    padding: 1rem 1.25rem;
    font-size: 0.95rem;
    gap: 0.5rem;
  }
  
  .question-number {
    min-width: 20px;
    font-size: 0.9rem;
  }
  
  .faq-icon {
    width: 28px;
    height: 28px;
  }
  
  .answer-content {
    padding: 0 1.25rem 1rem 2.5rem;
    font-size: 0.9rem;
  }
  
  .faq-list {
    gap: 0.75rem;
  }
  
  .decoration-1,
  .decoration-2 {
    display: none;
  }
  
  .decoration-triangle {
    display: none;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .faq-item,
  .faq-question,
  .faq-icon,
  .faq-answer {
    transition: none;
  }
  
  .decoration-1,
  .decoration-2,
  .decoration-triangle {
    animation: none;
  }
}

/* Focus states for accessibility */
.faq-question:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.faq-question:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .faq-item {
    border: 2px solid #000;
  }
  
  .faq-question {
    color: #000;
  }
  
  .answer-content p {
    color: #000;
  }
}
