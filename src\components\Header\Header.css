/* .header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 40px;
  background: #fff;
  border-bottom: 1px solid #eee;
  position: relative;
}

.logo {
  font-size: 20px;
  font-weight: bold;
  color: #007bff;
}

.nav ul {
  display: flex;
  list-style: none;
  gap: 24px;
}

.nav ul li a {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: color 0.3s;
}

.nav ul li a:hover {
  color: #007bff;
}

.header-buttons {
  display: flex;
  gap: 12px;
}

.btn-outline {
  border: 1px solid #333;
  background: transparent;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
}

.btn-primary {
  background: #0a66c2;
  color: #fff;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
}

.menu-toggle {
  display: none;
  font-size: 26px;
  cursor: pointer;
}

@media (max-width: 900px) {
  .nav {
    display: none;
    position: absolute;
    top: 60px;
    left: 0;
    right: 0;
    background: #fff;
    padding: 20px;
    border-top: 1px solid #eee;
  }

  .nav.open {
    display: block;
  }

  .nav ul {
    flex-direction: column;
    gap: 16px;
  }

  .header-buttons {
    display: none;
  }

  .menu-toggle {
    display: block;
  }
} */


/* ---------------- HEADER LAYOUT ---------------- */
/* .header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 40px;
  background: #fff;
  border-bottom: 1px solid #eee;
  position: relative;
}

.logo {
  font-size: 20px;
  font-weight: bold;
  color: #0a66c2;
}

.nav ul {
  display: flex;
  list-style: none;
  gap: 24px;
  align-items: center;
}

.nav ul li a {
  text-decoration: none;
  color: #333;
  font-weight: 500;
}

.header-buttons {
  display: flex;
  gap: 12px;
}

.btn-outline {
  border: 1px solid #333;
  background: transparent;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
}

.btn-primary {
  background: #0a66c2;
  color: #fff;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
}

.dropdown {
  position: relative;
}

.dropdown a {
  cursor: pointer;
}

.dropdown ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.dropdown-product {
  position: absolute;
  top: 100%;
  left: 0;
  display: flex;
  gap: 40px;
  background: #fff;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0px 4px 12px rgba(0,0,0,0.1);
  width: 90vw;
  max-width: 1200px;
  z-index: 1000;
}

.product-left,
.product-center,
.product-right {
  flex: 1;
  font-size: 14px;
}

.product-left img {
  margin-top: 15px;
  max-width: 100%;
  border-radius: 6px;
}

.product-center ul,
.product-right ul {
  list-style: none;
  padding: 0;
}

.product-center ul li,
.product-right ul li {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.dropdown-resources {
  position: absolute;
  top: 100%;
  left: 0;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0px 4px 12px rgba(0,0,0,0.1);
  padding: 12px 0;
  min-width: 260px;
  z-index: 1000;
}

.dropdown-resources li {
  padding: 10px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: background 0.3s;
}

.dropdown-resources li:hover {
  background: #f5f7fb;
}

.dropdown-company {
  position: absolute;
  top: 100%;
  left: 0;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0px 4px 12px rgba(0,0,0,0.1);
  padding: 12px 0;
  min-width: 180px;
  z-index: 1000;
}

.dropdown-company li {
  padding: 10px 16px;
  cursor: pointer;
  transition: background 0.3s;
}

.dropdown-company li:hover {
  background: #f5f7fb;
}

.menu-toggle {
  display: none;
  cursor: pointer;
  font-size: 20px;
}

@media (max-width: 900px) {
  .nav {
    display: none;
  }
  .nav.open {
    display: block;
  }
  .nav ul {
    flex-direction: column;
    gap: 12px;
  }
  .header-buttons {
    display: none;
  }
  .menu-toggle {
    display: block;
  }
} */



.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 30px;
  background-color: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-wrap: wrap;
}

.logo img {
  height: 40px;
}

.nav-links {
  display: flex;
  gap: 20px;
  align-items: center;
}

.nav-links a, .dropbtn {
  text-decoration: none;
  color: #1a2c5b;
  font-weight: 500;
  cursor: pointer;
  background: none;
  border: none;
  padding: 5px 10px;
}

.dropdown {
  position: relative;
}

.dropdown-content {
  display: none;
  position: absolute;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  min-width: 160px;
  z-index: 1;
}

.dropdown-content a {
  display: block;
  padding: 10px;
  color: #333;
}

.dropdown:hover .dropdown-content {
  display: block;
}

.auth-buttons {
  display: flex;
  gap: 15px;
  align-items: center;
}

.free-account {
  background-color: #0f3c82;
  color: white;
  padding: 8px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.login {
  background: none;
  border: 1px solid #0f3c82;
  padding: 8px 20px;
  border-radius: 5px;
  color: #0f3c82;
  cursor: pointer;
}

@media (max-width: 768px) {
  .nav-links {
    flex-direction: column;
    gap: 10px;
    width: 100%;
  }

  .auth-buttons {
    flex-direction: column;
    width: 100%;
    gap: 10px;
    margin-top: 15px;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
  }
}
