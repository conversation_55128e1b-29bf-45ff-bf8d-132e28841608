.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 30px;
  background-color: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-wrap: wrap;
}

.logo img {
  height: 40px;
}

.nav-links {
  display: flex;
  gap: 20px;
  align-items: center;
}

.nav-links a, .dropbtn {
  text-decoration: none;
  color: #1a2c5b;
  font-weight: 500;
  cursor: pointer;
  background: none;
  border: none;
  padding: 5px 10px;
}

.dropdown {
  position: relative;
}

.dropdown-content {
  display: none;
  position: absolute;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  min-width: 160px;
  z-index: 1;
}

.dropdown-content a {
  display: block;
  padding: 10px;
  color: #333;
}

.dropdown:hover .dropdown-content {
  display: block;
}

.auth-buttons {
  display: flex;
  gap: 15px;
  align-items: center;
}

.free-account {
  background-color: #0f3c82;
  color: white;
  padding: 8px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.login {
  background: none;
  border: 1px solid #0f3c82;
  padding: 8px 20px;
  border-radius: 5px;
  color: #0f3c82;
  cursor: pointer;
}

@media (max-width: 768px) {
  .nav-links {
    flex-direction: column;
    gap: 10px;
    width: 100%;
  }

  .auth-buttons {
    flex-direction: column;
    width: 100%;
    gap: 10px;
    margin-top: 15px;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
  }
}
