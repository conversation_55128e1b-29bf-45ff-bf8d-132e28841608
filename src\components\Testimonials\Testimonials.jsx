// import React from "react";
// import "./Testimonials.css";
// import { FaStar, FaGoogle, FaCrown, FaUserCircle } from "react-icons/fa";


// const CapterraIcon = ({ size = 20 }) => (
//   <svg
//     width={size}
//     height={size}
//     viewBox="0 0 24 24"
//     xmlns="http://www.w3.org/2000/svg"
//     fill="none"
//   >
//     <path d="M22.5 3L8 12.5L12.5 14.5L14.5 19L22.5 3Z" fill="#1F70C1" />
//     <path d="M1.5 3L14.5 19L12.5 14.5L8 12.5L1.5 3Z" fill="#F68D2E" />
//   </svg>
// );

// const testimonials = [
//   {
//     text: `“It’s been a really positive experience overall. I use LeadCRM to build qualified leads from LinkedIn and sync them into HubSpot. The platform has saved me hours of manual work every week. I would especially highlight their customer support — they are fast, knowledgeable, and genuinely helpful.”`,
//     name: "<PERSON>",
//     platform: "On Capterra",
//     avatar: <FaUserCircle size={32} color="#555" />,
//     icon: <img src={capterraLogo} alt="Capterra" className="testimonial-icon" />,
//   },
//   {
//     text: `“Keeps Me Organized! I never lose track of a lead now that I can see and create tasks right in LinkedIn. The inbuilt enrichment is excellent, and when it misses anything, integrations fill in the gaps.”`,
//     name: "Paul Kevin",
//     platform: "On Google",
//     avatar: <FaCrown size={32} color="#e63946" />,
//     icon: <FaGoogle size={20} color="#db4437" />,
//   },
//   {
//     text: `“Hunter.io Integration is Great! I love that LeadCRM uses multiple sources, including its own. If one source doesn’t have the data, the next source does. Message templates help me send quick, consistent messages.”`,
//     name: "Ruthie Smith",
//     platform: "On G2",
//     avatar: <FaCrown size={32} color="#6a4c93" />,
//     icon: <FaGoogle size={20} color="#0f9d58" />, // 👈 you can replace with G2 SVG later
//   },
// ];

// const Testimonials = () => {
//   return (
//     <section className="testimonials">
//       <h2 className="testimonials-title">
//         See Why Top Performers Choose LeadCRM
//       </h2>

//       <div className="testimonials-container">
//         {testimonials.map((t, index) => (
//           <div className="testimonial-card" key={index}>
//             <p className="testimonial-text">{t.text}</p>
//             <div className="testimonial-rating">
//               {t.icon}
//               <span className="stars">
//                 {[...Array(5)].map((_, i) => (
//                   <FaStar key={i} color="#f1c40f" />
//                 ))}
//               </span>
//             </div>
//             <div className="testimonial-footer">
//               <div className="testimonial-info">
//                 <strong>{t.name}</strong>
//                 <span>{t.platform}</span>
//               </div>
//               <div className="testimonial-avatar">{t.avatar}</div>
//             </div>
//           </div>
//         ))}
//       </div>
//     </section>
//   );
// };

// export default Testimonials;
