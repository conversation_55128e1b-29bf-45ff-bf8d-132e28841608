/* Wrapper */
.fc {
  width: 100%;
  overflow: hidden;
  position: relative;
  padding: 10px 0;
  background: #f9f9f9; 
}

.fc-track {
  display: flex;
  align-items: center;
  gap: clamp(14px, 3vw, 28px);
  width: max-content;
  animation: scroll-left 25s linear infinite;
}

.fc:hover .fc-track {
  animation-play-state: paused;
}

.fc-item {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 14px;
  font-weight: 500;
  font-size: clamp(12px, 1.05vw, 14px);
  min-width: clamp(160px, 34vw, 280px);
  white-space: nowrap;
}

.fc-icon {
  font-size: 20px;
  line-height: 1;
  width: 24px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fc-icon img {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

/* Animation: infinite right → left */
@keyframes scroll-left {
  from { transform: translateX(0); }
  to   { transform: translateX(-50%); }
}

@media (prefers-reduced-motion: reduce) {
  .fc-track { animation: none; }
}
