// import React from "react";
// import "./Comparison.css";
// import { FaTimesCircle, FaCheckCircle, FaDatabase, FaRobot, FaSyncAlt } from "react-icons/fa";

// const Comparison = () => {
//   return (
//     <section className="comparison">
//       <h2 className="comparison-title">
//         Stop Letting a Broken Workflow Dictate Your Sales Results.
//       </h2>

//       <div className="comparison-container">
//         <div className="card without">
//           <h3>Without LeadCRM</h3>
//           <ul>
//             <li><FaTimesCircle className="icon red" /> <b>Manual Data Entry</b> – Copying LinkedIn contacts manually (3+ hours wasted daily).</li>
//             <li><FaTimesCircle className="icon red" /> <b>Incomplete Data</b> – Missing emails & phones (60% data incomplete).</li>
//             <li><FaTimesCircle className="icon red" /> <b>No CRM Visibility</b> – Can’t see CRM contacts when browsing LinkedIn profiles.</li>
//             <li><FaTimesCircle className="icon red" /> <b>Limited Productivity</b> – Writing messages manually, no AI help.</li>
//           </ul>
//         </div>

//         <div className="card with">
//           <h3>With LeadCRM</h3>
//           <ul>
//             <li><FaCheckCircle className="icon green" /> <b>Complete Bi-Directional Sync</b> – Auto sync contacts, messages & notes.</li>
//             <li><FaCheckCircle className="icon green" /> <b>700M+ Contacts + Enrichment</b> – Verified emails & numbers from vast database.</li>
//             <li><FaCheckCircle className="icon green" /> <b>CRM Overlay on LinkedIn</b> – See CRM insights directly on LinkedIn profiles.</li>
//             <li><FaCheckCircle className="icon green" /> <b>AI Response + Templates</b> – Reply faster with pre-built templates & bulk exports.</li>
//           </ul>
//         </div>
//       </div>

//       <div className="cta">
//         <button className="btn-primary">Get a Free Account Now!</button>
//         <p className="save-text">✨ Save 90+ hours every Month</p>
//       </div>
//     </section>
//   );
// };

// export default Comparison;


// import React from "react";
// import "./Comparison.css";

// const Comparison = () => {
//   return (
//     <section className="comparison-section">
//       <h2>Stop Letting a Broken Workflow Dictate Your Sales Results.</h2>

//       <div className="comparison-cards">
//         <div className="card without-leadcrm">
//           <div className="card-image">
//             <img src="without-leadcrm.png" alt="Without LeadCRM" />
//           </div>
//           <h3>Without LeadCRM</h3>
//           <ul>
//             <li><strong>Manual Data Entry</strong><br />Copying LinkedIn contacts to CRM manually plus losing conversation history</li>
//             <li><strong>Incomplete Data</strong><br />LinkedIn profiles missing Email and Phones from 700M+ Database</li>
//             <li><strong>No CRM Visibility</strong><br />Can't see existing CRM contacts when browsing LinkedIn profiles</li>
//             <li><strong>Limited Productivity</strong><br />Writing messages manually plus no AI assistant</li>
//           </ul>
//         </div>

//         <div className="vs-divider">VS</div>

//         <div className="card with-leadcrm">
//           <div className="card-image">
//             <img src="with-leadcrm.png" alt="With LeadCRM" />
//           </div>
//           <h3>With LeadCRM</h3>
//           <ul>
//             <li><strong>Complete Bi-Directional Sync</strong><br />Automatically sync contacts, messages, and notes in real time</li>
//             <li><strong>700M+ Contacts + Enrichment</strong><br />Get verified emails and phone numbers from a vast global database</li>
//             <li><strong>CRM Overlay on LinkedIn</strong><br />See full CRM insights directly on LinkedIn profiles without switching tabs</li>
//             <li><strong>AI Response + Templates + Bulk Exports</strong><br />Save time with AI-crafted replies, pre-built templates, and one-click exports</li>
//           </ul>
//         </div>
//       </div>

//       <button className="cta-button">Get a Free Account Now!</button>

//       <p className="small-note">Save 40+ hours every Month</p>
//     </section>
//   );
// };

// export default Comparison;



import React from "react";
import "./Comparison.css";
import without1 from '../../assets/without1.png';
import without2 from '../../assets/without2.png';

const Comparison = () => {
  return (
    <section className="comparison-section">
      <h2>Stop Letting a Broken Workflow Dictate Your Sales Results.</h2>

      <div className="comparison-cards">
        <div className="card without-leadcrm">
          <div className="card-image">
            <img src={without1} alt="Without LeadCRM Workflow" />
          </div>
          <h3>Without LeadCRM</h3>
          <ul>
            <li>
              <img src="icon-cross.png" alt="Cross Icon" className="icon" />
              <div>
                <strong>Manual Data Entry</strong>
                <p>Copying LinkedIn contacts to CRM manually plus losing conversation history</p>
              </div>
            </li>
            <li>
              <img src="icon-cross.png" alt="Cross Icon" className="icon" />
              <div>
                <strong>Incomplete Data</strong>
                <p>LinkedIn profiles missing Email and Phones from 700M+ Database</p>
              </div>
            </li>
            <li>
              <img src="icon-cross.png" alt="Cross Icon" className="icon" />
              <div>
                <strong>No CRM Visibility</strong>
                <p>Can't see existing CRM contacts when browsing LinkedIn profiles</p>
              </div>
            </li>
            <li>
              <img src="icon-cross.png" alt="Cross Icon" className="icon" />
              <div>
                <strong>Limited Productivity</strong>
                <p>Writing messages manually plus no AI assistant</p>
              </div>
            </li>
          </ul>
        </div>

        <div className="vs-divider">VS</div>

        <div className="card with-leadcrm">
          <div className="card-image">
            <img src={without2} alt="With LeadCRM Workflow" />
          </div>
          <h3>With LeadCRM</h3>
          <ul>
            <li>
              <img src="icon-check.png" alt="Check Icon" className="icon" />
              <div>
                <strong>Complete Bi-Directional Sync</strong>
                <p>Automatically sync contacts, messages, and notes in real time</p>
              </div>
            </li>
            <li>
              <img src="icon-check.png" alt="Check Icon" className="icon" />
              <div>
                <strong>700M+ Contacts + Enrichment</strong>
                <p>Get verified emails and phone numbers from a vast global database</p>
              </div>
            </li>
            <li>
              <img src="icon-check.png" alt="Check Icon" className="icon" />
              <div>
                <strong>CRM Overlay on LinkedIn</strong>
                <p>See full CRM insights directly on LinkedIn profiles without switching tabs</p>
              </div>
            </li>
            <li>
              <img src="icon-check.png" alt="Check Icon" className="icon" />
              <div>
                <strong>AI Response + Templates + Bulk Exports</strong>
                <p>Save time with AI-crafted replies, pre-built templates, and one-click exports</p>
              </div>
            </li>
          </ul>
        </div>
      </div>

      <button className="cta-button">Get a Free Account Now!</button>

      <p className="small-note">Save 40+ hours every Month</p>
    </section>
  );
};

export default Comparison;
