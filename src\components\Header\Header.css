/* Base Header Styles */
.header {
  position: sticky;
  top: 0;
  background-color: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  width: 100%;
}

.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 30px;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
}

.logo {
  flex-shrink: 0;
  z-index: 1003;
}

.logo img {
  height: 40px;
  width: auto;
  display: block;
}

/* Desktop Navigation */
.nav-links {
  display: flex;
  gap: 20px;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-links > a,
.nav-links .dropbtn {
  text-decoration: none;
  color: #1a2c5b;
  font-weight: 500;
  cursor: pointer;
  background: none;
  border: none;
  padding: 10px 15px;
  border-radius: 6px;
  transition: all 0.3s ease;
  font-size: 16px;
  white-space: nowrap;
}

.nav-links > a:hover,
.nav-links .dropbtn:hover {
  background-color: #f8f9fa;
  color: #0f3c82;
}

/* Dropdown Styles */
.dropdown {
  position: relative;
}

.dropdown-content {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  background-color: white;
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  min-width: 200px;
  border-radius: 8px;
  padding: 10px 0;
  z-index: 1001;
  border: 1px solid #e9ecef;
}

.dropdown-content a {
  display: block;
  padding: 12px 20px;
  color: #333;
  text-decoration: none;
  transition: all 0.2s ease;
  border-radius: 0;
  margin: 0;
}

.dropdown-content a:hover {
  background-color: #f8f9fa;
  color: #0f3c82;
}

/* Desktop hover behavior */
@media (min-width: 769px) {
  .dropdown:hover .dropdown-content {
    display: block;
  }
}

/* Show dropdown when active (mobile) */
.dropdown-content.show {
  display: block !important;
}

/* Auth Buttons */
.auth-buttons {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-shrink: 0;
}

.desktop-auth {
  display: flex;
}

.mobile-auth-buttons {
  display: none;
  flex-direction: column;
  gap: 15px;
  width: 100%;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.free-account {
  background-color: #0f3c82;
  color: white;
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.free-account:hover {
  background-color: #0d3470;
  transform: translateY(-1px);
}

.login {
  background: transparent;
  border: 2px solid #0f3c82;
  padding: 10px 24px;
  border-radius: 6px;
  color: #0f3c82;
  cursor: pointer;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.login:hover {
  background-color: #0f3c82;
  color: white;
  transform: translateY(-1px);
}

/* Mobile Menu Toggle */
.menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: space-between;
  width: 32px;
  height: 24px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: 1003;
  position: relative;
}

.hamburger-line {
  width: 100%;
  height: 3px;
  background-color: #1a2c5b;
  border-radius: 2px;
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  transform-origin: center;
}

.menu-toggle.active .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(7px, 7px);
  background-color: #0f3c82;
}

.menu-toggle.active .hamburger-line:nth-child(2) {
  opacity: 0;
  transform: scale(0);
}

.menu-toggle.active .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -7px);
  background-color: #0f3c82;
}

/* Mobile Menu Overlay */
.menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 998;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.menu-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Tablet and Mobile Responsive Styles */
@media (max-width: 1024px) {
  .header-container {
    padding: 15px 25px;
  }

  .nav-links {
    gap: 15px;
  }

  .nav-links > a,
  .nav-links .dropbtn {
    padding: 8px 12px;
    font-size: 15px;
  }
}

@media (max-width: 768px) {
  .header-container {
    padding: 15px 20px;
  }

  /* Show mobile menu toggle */
  .menu-toggle {
    display: flex;
  }

  /* Hide desktop auth buttons */
  .desktop-auth {
    display: none !important;
  }

  /* Show mobile auth buttons */
  .mobile-auth-buttons {
    display: flex;
  }

  /* Mobile Navigation */
  .nav-links {
    position: fixed;
    top: 0;
    right: -100%;
    width: 320px;
    max-width: 85vw;
    height: 100vh;
    background-color: #ffffff;
    flex-direction: column;
    align-items: stretch;
    padding: 80px 0 30px 0;
    box-shadow: -5px 0 20px rgba(0, 0, 0, 0.15);
    transition: right 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    z-index: 999;
    overflow-y: auto;
    gap: 0;
  }

  .nav-links.nav-open {
    right: 0;
  }

  /* Mobile Navigation Links */
  .nav-links > a,
  .nav-links .dropbtn {
    width: 100%;
    text-align: left;
    padding: 18px 30px;
    border-bottom: 1px solid #f0f2f5;
    font-size: 16px;
    font-weight: 500;
    color: #1a2c5b;
    border-radius: 0;
    background: none;
    transition: all 0.3s ease;
  }

  .nav-links > a:hover,
  .nav-links .dropbtn:hover {
    background-color: #f8f9fa;
    color: #0f3c82;
    padding-left: 35px;
  }

  /* Mobile Dropdown */
  .dropdown {
    width: 100%;
  }

  .dropdown-content {
    position: static;
    display: none;
    box-shadow: none;
    background-color: #f8f9fa;
    border: none;
    border-radius: 0;
    padding: 0;
    margin: 0;
  }

  .dropdown-content.show {
    display: block;
    animation: slideDown 0.3s ease;
  }

  .dropdown-content a {
    padding: 15px 50px;
    font-size: 15px;
    color: #666;
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
  }

  .dropdown-content a:hover {
    background-color: #e9ecef;
    color: #0f3c82;
    padding-left: 55px;
  }

  .dropdown-content a:last-child {
    border-bottom: none;
  }

  /* Mobile Auth Buttons */
  .mobile-auth-buttons {
    padding: 20px 30px;
    gap: 15px;
  }

  .mobile-auth-buttons .free-account,
  .mobile-auth-buttons .login {
    width: 100%;
    text-align: center;
    padding: 15px 20px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
  }

  .mobile-auth-buttons .free-account {
    background-color: #0f3c82;
    color: white;
    border: none;
  }

  .mobile-auth-buttons .login {
    background: transparent;
    color: #0f3c82;
    border: 2px solid #0f3c82;
  }
}

@media (max-width: 480px) {
  .header-container {
    padding: 12px 15px;
  }

  .logo img {
    height: 35px;
  }

  .menu-toggle {
    width: 28px;
    height: 22px;
  }

  .nav-links {
    width: 100%;
    max-width: 100vw;
  }

  .nav-links > a,
  .nav-links .dropbtn {
    padding: 16px 20px;
    font-size: 15px;
  }

  .nav-links > a:hover,
  .nav-links .dropbtn:hover {
    padding-left: 25px;
  }

  .dropdown-content a {
    padding: 12px 40px;
    font-size: 14px;
  }

  .dropdown-content a:hover {
    padding-left: 45px;
  }

  .mobile-auth-buttons {
    padding: 15px 20px;
  }

  .mobile-auth-buttons .free-account,
  .mobile-auth-buttons .login {
    padding: 12px 15px;
    font-size: 15px;
  }
}

@media (max-width: 360px) {
  .header-container {
    padding: 10px 12px;
  }

  .logo img {
    height: 32px;
  }

  .nav-links > a,
  .nav-links .dropbtn {
    padding: 14px 15px;
    font-size: 14px;
  }

  .dropdown-content a {
    padding: 10px 30px;
    font-size: 13px;
  }
}

/* Smooth animations */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Prevent body scroll when menu is open */
body.menu-open {
  overflow: hidden;
}
