import React, { useState } from 'react';
import './FAQ.css';

const FAQ = () => {
  const [openItems, setOpenItems] = useState({ 0: true }); 

  const faqData = [
    {
      question: "What exactly does LeadCRM do?",
      answer: "LeadCRM is a browser extension that seamlessly connects your LinkedIn account including Sales Navigator to your CRM. It eliminates manual data entry by allowing you to instantly sync entire LinkedIn profiles - including contact information, experience, and skills - to your CRM in a single click. It's designed to save time and improve data accuracy for sales professionals."
    },
    {
      question: "Which CRMs do you integrate with?",
      answer: "LeadCRM integrates with all major CRM platforms including HubSpot, Salesforce, Pipedrive, Zoho CRM, Copper CRM, Google Sheets, Close.io, and many more. We're constantly adding new integrations based on user requests."
    },
    {
      question: "Can I integrate any CRM if it's not supported natively?",
      answer: "Yes! Even if your CRM isn't natively supported, you can still use LeadCRM through our Zapier integration or custom API connections. We also offer custom integration development for enterprise clients."
    },
    {
      question: "Can I control what information gets synced to my CRM?",
      answer: "Absolutely! LeadCRM gives you full control over what data gets synced. You can customize field mappings, choose which information to include or exclude, and set up automated rules for data synchronization."
    },
    {
      question: "How is LeadCRM different from other tools?",
      answer: "LeadCRM stands out with its seamless LinkedIn integration, one-click data sync, comprehensive CRM support, and focus on data accuracy. Unlike other tools, we maintain real-time sync and offer advanced customization options."
    },
    {
      question: "Is LeadCRM safe and GDPR compliant?",
      answer: "Yes, LeadCRM is fully GDPR compliant and follows strict data security protocols. We use enterprise-grade encryption, secure data transmission, and comply with all major privacy regulations including GDPR, CCPA, and SOC 2."
    },
    {
      question: "What kind of support can I expect from LeadCRM?",
      answer: "We provide comprehensive support including 24/7 customer service, detailed documentation, video tutorials, onboarding assistance, and dedicated account management for enterprise clients. Our support team is always ready to help you maximize your LeadCRM experience."
    }
  ];

  const toggleItem = (index) => {
    setOpenItems(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  };

  return (
    <section className="faq-section">
      <div className="faq-container">
        <div className="faq-header">
          <h2 className="faq-title">Frequently Asked Questions</h2>
        </div>
        
        <div className="faq-list">
          {faqData.map((item, index) => (
            <div key={index} className={`faq-item ${openItems[index] ? 'open' : ''}`}>
              <button 
                className="faq-question"
                onClick={() => toggleItem(index)}
                aria-expanded={openItems[index]}
                aria-controls={`faq-answer-${index}`}
              >
                <span className="question-number">{index + 1}.</span>
                <span className="question-text">{item.question}</span>
                <span className="faq-icon">
                  {openItems[index] ? (
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                      <path d="M18 12H6" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                    </svg>
                  ) : (
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                      <path d="M12 6v12M6 12h12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                    </svg>
                  )}
                </span>
              </button>
              
              <div 
                id={`faq-answer-${index}`}
                className="faq-answer"
                style={{
                  maxHeight: openItems[index] ? '200px' : '0',
                  opacity: openItems[index] ? '1' : '0'
                }}
              >
                <div className="answer-content">
                  <p>{item.answer}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      {/* Decorative Elements */}
      <div className="faq-decoration">
        <div className="decoration-circle decoration-1"></div>
        <div className="decoration-circle decoration-2"></div>
        <div className="decoration-triangle"></div>
      </div>
    </section>
  );
};

export default FAQ;
