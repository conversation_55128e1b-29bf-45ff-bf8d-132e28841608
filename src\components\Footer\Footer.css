/* Footer Component Styles */
.footer {
  background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
  padding: 3rem 0 1rem;
  position: relative;
  margin-top: auto;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1.2fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

/* Footer Brand Section */
.footer-brand {
  max-width: 280px;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.footer-logo-image {
  width: 32px;
  height: 32px;
}

.footer-logo-text {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e40af;
}

.footer-description {
  color: #475569;
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 1.5rem;
}

.footer-social {
  display: flex;
  gap: 0.75rem;
}

.social-link {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  text-decoration: none;
}

.social-link.facebook {
  background: #1877f2;
  color: white;
}

.social-link.twitter {
  background: #1da1f2;
  color: white;
}

.social-link.linkedin {
  background: #0077b5;
  color: white;
}

.social-link.instagram {
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  color: white;
}

.social-link:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Footer Sections */
.footer-section {
  display: flex;
  flex-direction: column;
}

.footer-section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e40af;
  margin-bottom: 1rem;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 0.5rem;
}

.footer-links a {
  color: #475569;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #1e40af;
}

.footer-links a.legal-link {
  color: #0ea5e9;
}

.footer-links a.legal-link:hover {
  color: #0284c7;
}

/* Contact Section */
.contact-section {
  max-width: 300px;
}

.contact-info {
  margin-bottom: 1.5rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  color: #475569;
  font-size: 0.9rem;
}

.contact-icon {
  color: #64748b;
  flex-shrink: 0;
}

/* Chrome Badge */
.chrome-badge {
  background: #1e293b;
  border-radius: 12px;
  padding: 0.75rem 1rem;
  display: inline-block;
}

.badge-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.badge-text {
  color: #94a3b8;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-chrome {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: white;
  font-weight: 500;
  font-size: 0.9rem;
}

.chrome-icon {
  width: 20px;
  height: 20px;
}

/* Footer Bottom */
.footer-bottom {
  border-top: 1px solid rgba(71, 85, 105, 0.2);
  padding-top: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.footer-disclaimer {
  flex: 1;
  max-width: 800px;
}

.footer-disclaimer p {
  color: #64748b;
  font-size: 0.8rem;
  line-height: 1.4;
  margin-bottom: 0.5rem;
}

.privacy-link-bottom a {
  color: #0ea5e9;
  text-decoration: none;
  font-size: 0.8rem;
}

.privacy-link-bottom a:hover {
  text-decoration: underline;
}

.footer-copyright {
  flex-shrink: 0;
}

.footer-copyright p {
  color: #64748b;
  font-size: 0.8rem;
  margin: 0;
}

/* Chat Widget */
.chat-widget {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 1000;
}

.chat-bubble {
  width: 56px;
  height: 56px;
  background: #1e293b;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  position: relative;
}

.chat-bubble:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.chat-count {
  background: #ef4444;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: -2px;
  right: -2px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .footer-content {
    grid-template-columns: 1fr 1fr 1fr;
    gap: 1.5rem;
  }
  
  .footer-brand {
    grid-column: 1 / -1;
    max-width: 100%;
    margin-bottom: 1rem;
  }
  
  .contact-section {
    grid-column: 1 / -1;
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .footer {
    padding: 2rem 0 1rem;
  }
  
  .footer-container {
    padding: 0 1rem;
  }
  
  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }
  
  .footer-brand {
    grid-column: 1 / -1;
  }
  
  .contact-section {
    grid-column: 1 / -1;
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .footer-social {
    justify-content: center;
  }
  
  .chrome-badge {
    align-self: center;
  }
}

@media (max-width: 480px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .footer-brand {
    text-align: center;
  }
  
  .footer-logo {
    justify-content: center;
  }
  
  .footer-section {
    text-align: center;
  }
  
  .contact-item {
    justify-content: center;
  }
  
  .chat-widget {
    bottom: 1rem;
    right: 1rem;
  }
  
  .chat-bubble {
    width: 48px;
    height: 48px;
  }
  
  .chat-count {
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
  }
}

/* Touch devices */
@media (hover: none) and (pointer: coarse) {
  .social-link:hover {
    transform: none;
    box-shadow: none;
  }
  
  .chat-bubble:hover {
    transform: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}
