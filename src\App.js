import './App.css';
import Header from './components/Header/Header';
import Hero from './components/Hero/Hero';

import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import FeatureCarousel from './components/FeatureCarousel/FeatureCarousel';
import Comparison from './components/Comparison/Comparison';
import HowItWorks from './components/HowItWorks/HowItWorks';
import Footer from './components/Footer/Footer';
import FAQ from './components/FAQ/FAQ';
import Integration from './components/Integration/Integration';
import LinkedInSolutions from './components/LinkedInSolutions/LinkedInSolutions';
import Testimonials from './components/Testimonials/Testimonials';
import CrmIntegration from './components/CrmIntegration/CrmIntegration';


function App() {
  return (
    <>
      <Header />
      <Hero />
      <FeatureCarousel />
      <Comparison />
      <HowItWorks />
      <Testimonials />
      <LinkedInSolutions />
      <CrmIntegration />
      <Integration />
      <FAQ />
      <Footer />
    </>
  );
}

export default App;
