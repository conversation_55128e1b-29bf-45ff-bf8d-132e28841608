.testimonials {
  padding: 60px 20px;
  background: #f9fbff;
  text-align: center;
}

.testimonials-title {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 40px;
  color: #1a1a1a;
}

.testimonials-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
  justify-content: center;
}

.testimonial-card {
  background: #fff;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.06);
  text-align: left;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.testimonial-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.testimonial-text {
  font-size: 15px;
  line-height: 1.6;
  margin-bottom: 16px;
  color: #333;
}

.testimonial-rating {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 16px;
}

.testimonial-icon {
  height: 20px;
  width: auto;
}

.stars {
  display: flex;
  gap: 4px;
}

.testimonial-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.testimonial-info {
  display: flex;
  flex-direction: column;
}

.testimonial-info strong {
  font-size: 16px;
  color: #1a1a1a;
}

.testimonial-info span {
  font-size: 14px;
  color: #777;
}

.testimonial-avatar {
  font-size: 32px;
}
