// import React from "react";
// import "./HowItWorks.css";

// const steps = [
//   {
//     number: "1",
//     title: "Install the Extension",
//     description:
//       "Add LeadCRM to Chrome and connect your CRM in 2 minutes – secure and seamless.",
//     image: "https://via.placeholder.com/250x120.png?text=Extension+Setup", // replace with your image
//   },
//   {
//     number: "2",
//     title: "Browse LinkedIn",
//     description:
//       "Use LinkedIn normally, our extension auto-captures data and reveals CRM contacts instantly.",
//     image: "https://via.placeholder.com/250x120.png?text=LinkedIn+Browse", // replace with your image
//   },
//   {
//     number: "3",
//     title: "Get Enriched Data",
//     description:
//       "Enrich profiles with verified emails, phones, and company data with 95%+ accuracy guaranteed.",
//     image: "https://via.placeholder.com/250x120.png?text=Enriched+Data", // replace with your image
//   },
//   {
//     number: "4",
//     title: "Sync to CRM Instantly",
//     description:
//       "Prospect data syncs to your CRM instantly with history, tracking, and AI insights for follow ups.",
//     image: "https://via.placeholder.com/250x120.png?text=CRM+Sync", // replace with your image
//   },
// ];

// const HowItWorks = () => {
//   return (
//     <section className="howitworks">
//       <h2 className="title">
//         How it Works
//         <span> From setup to success in <b>4 simple steps</b></span>
//       </h2>

//       <div className="steps-container">
//         {steps.map((step, index) => (
//           <div className="step-card" key={index}>
//             <div className="step-number">{step.number}</div>
//             <h3>{step.title}</h3>
//             <p>{step.description}</p>
//             <img src={step.image} alt={step.title} />
//           </div>
//         ))}
//       </div>

//       <div className="cta">
//         <button className="btn-primary">Try LeadCRM Now →</button>
//       </div>
//     </section>
//   );
// };

// export default HowItWorks;




// import React from "react";
// import "../styles/HowItWorks.css";

// // Import your real images
// import step1Img from "../assets/howitworks/step1.png";
// import step2Img from "../assets/howitworks/step2.png";
// import step3Img from "../assets/howitworks/step3.png";
// import step4Img from "../assets/howitworks/step4.png";

// const steps = [
//   {
//     number: "1",
//     title: "Install the Extension",
//     description:
//       "Add LeadCRM to Chrome and connect your CRM in 2 minutes – secure and seamless.",
//     image: step1Img,
//   },
//   {
//     number: "2",
//     title: "Browse LinkedIn",
//     description:
//       "Use LinkedIn normally, our extension auto-captures data and reveals CRM contacts instantly.",
//     image: step2Img,
//   },
//   {
//     number: "3",
//     title: "Get Enriched Data",
//     description:
//       "Enrich profiles with verified emails, phones, and company data with 95%+ accuracy guaranteed.",
//     image: step3Img,
//   },
//   {
//     number: "4",
//     title: "Sync to CRM Instantly",
//     description:
//       "Prospect data syncs to your CRM instantly with history, tracking, and AI insights for follow ups.",
//     image: step4Img,
//   },
// ];

// const HowItWorks = () => {
//   return (
//     <section className="howitworks">
//       <h2 className="title">
//         How it Works
//         <span> From setup to success in <b>4 simple steps</b></span>
//       </h2>

//       <div className="steps-container">
//         {steps.map((step, index) => (
//           <div className="step-card" key={index}>
//             <div className="step-number">{step.number}</div>
//             <h3>{step.title}</h3>
//             <p>{step.description}</p>
//             <img src={step.image} alt={step.title} />
//           </div>
//         ))}
//       </div>

//       <div className="cta">
//         <button className="btn-primary">Try LeadCRM Now →</button>
//       </div>
//     </section>
//   );
// };

// export default HowItWorks;


import React from "react";
import "./HowItWorks.css";

const HowItWorks = () => {
  return (
    <section className="how-it-works">
      <h2>How it Works</h2>
      <p>From setup to success in <strong>4 simple steps</strong></p>

      <div className="steps-container">
        <div className="step">
          <div className="step-number">1</div>
          <h3>Install the Extension</h3>
          <p>Add LeadCRM to Chrome and connect your CRM in 2 minutes – secure and seamless.</p>
          <img src="step1-image.png" alt="Install the Extension" />
        </div>

        <div className="step">
          <div className="step-number">2</div>
          <h3>Browse LinkedIn</h3>
          <p>Use LinkedIn normally, our auto-captures data and reveals CRM contacts instantly.</p>
          <img src="step2-image.png" alt="Browse LinkedIn" />
        </div>

        <div className="step">
          <div className="step-number">3</div>
          <h3>Get Enriched Data</h3>
          <p>Enrich profiles with verified emails, phones, and company data with 95%+ accuracy guaranteed.</p>
          <img src="step3-image.png" alt="Enriched Data" />
        </div>

        <div className="step">
          <div className="step-number">4</div>
          <h3>Sync to CRM Instantly</h3>
          <p>Prospect data syncs to your CRM instantly with history, tracking, and AI-powered insights.</p>
          <img src="step4-image.png" alt="Sync to CRM" />
        </div>
      </div>

      <button className="try-now">Try LeadCRM Now →</button>
    </section>
  );
};

export default HowItWorks;
