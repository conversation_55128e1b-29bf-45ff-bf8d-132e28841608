.hero {
  text-align: center;
  padding: 80px 20px;
  background: #f9fafe;
}

.tagline {
  display: inline-block;
  background: #eef6ff;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 14px;
  margin-bottom: 16px;
}

.hero h1 {
  font-size: 42px;
  font-weight: bold;
  margin-bottom: 20px;
}

.hero h1 .highlight {
  color: #0a66c2;
  text-decoration: underline;
}

.description {
  max-width: 600px;
  margin: 0 auto 30px auto;
  color: #555;
  font-size: 16px;
  line-height: 1.5;
}

/* Integrations */
.integration-box {
  display: inline-flex;
  align-items: center;
  gap: 15px;
  background: linear-gradient(to right, #b06ab3, #4568dc);
  color: #fff;
  padding: 14px 24px;
  border-radius: 12px;
  margin-bottom: 24px;
}

.hubspot-btn {
  background: #fff;
  color: #f2662f;
  border: none;
  padding: 8px 14px;
  border-radius: 6px;
  font-weight: bold;
  cursor: pointer;
}

/* Ratings */
.ratings {
  display: flex;
  justify-content: center;
  gap: 40px;
  margin-bottom: 24px;
  font-size: 18px;
}

/* CTA Buttons */
.cta-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.btn-dark {
  background: #222;
  color: #fff;
  padding: 12px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}

.btn-green {
  background: #28a745;
  color: #fff;
  padding: 12px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}

/* Responsive */
@media (max-width: 768px) {
  .hero h1 {
    font-size: 28px;
  }

  .description {
    font-size: 14px;
  }

  .ratings {
    flex-direction: column;
    gap: 12px;
  }

  .cta-buttons {
    flex-direction: column;
  }
}
