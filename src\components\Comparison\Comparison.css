.comparison-section {
  padding: 60px 20px;
  text-align: center;
}

.comparison-section h2 {
  font-size: 2rem;
  margin-bottom: 50px;
}

.comparison-cards {
  display: flex;
  justify-content: center;
  gap: 50px;
  flex-wrap: wrap;
  align-items: flex-start;
}

.card {
  background: white;
  padding: 25px;
  border-radius: 12px;
  flex: 1 1 300px;
  max-width: 400px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
  text-align: left;
}

.card-image {
  text-align: center;
  margin-bottom: 20px;
}

.card-image img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
}

.card h3 {
  font-size: 1.5rem;
  margin-bottom: 20px;
}

.card ul {
  list-style: none;
  padding: 0;
}

.card ul li {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 20px;
}

.card ul li .icon {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.cta-button {
  margin-top: 40px;
  background-color: #7dfc36;
  color: black;
  padding: 14px 30px;
  font-size: 1.1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}

.small-note {
  margin-top: 12px;
  font-style: italic;
  color: #555;
}

.vs-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 2rem;
  color: #888;
}

@media (max-width: 900px) {
  .comparison-cards {
    flex-direction: column;
    align-items: center;
  }

  .vs-divider {
    display: none;
  }
}
