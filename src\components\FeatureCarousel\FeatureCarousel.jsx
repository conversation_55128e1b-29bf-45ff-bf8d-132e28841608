// import React from "react";
// import Slider from "react-slick";
// import "./FeatureCarousel.css";

// import { FaUsers, FaGlobe, FaRobot, FaFileExport, FaProjectDiagram, FaEnvelope, FaDatabase } from "react-icons/fa";

// const features = [
//   { icon: <FaUsers />, title: "Access to 700M+ Contacts" },
//   { icon: <FaGlobe />, title: "Supports 9 Global Languages" },
//   { icon: <FaRobot />, title: "Advanced AI Productivity" },
//   { icon: <FaFileExport />, title: "Bulk Export & Enrich" },
//   { icon: <FaProjectDiagram />, title: "Seamless Deal Management" },
//   { icon: <FaEnvelope />, title: "Instant Email Finder" },
//   { icon: <FaDatabase />, title: "Advanced Waterfall Enrichment" },
// ];

// const FeatureCarousel = () => {
//   const settings = {
//     dots: false,
//     infinite: true,
//     speed: 500,
//     autoplay: true,
//     autoplaySpeed: 2500,
//     slidesToShow: 4,
//     slidesToScroll: 1,
//     responsive: [
//       { breakpoint: 1200, settings: { slidesToShow: 3 } },
//       { breakpoint: 900, settings: { slidesToShow: 2 } },
//       { breakpoint: 600, settings: { slidesToShow: 1 } },
//     ],
//   };

//   return (
//     <div className="carousel-container">
//       <Slider {...settings}>
//         {features.map((f, index) => (
//           <div key={index} className="carousel-item">
//             <div className="icon">{f.icon}</div>
//             <p>{f.title}</p>
//           </div>
//         ))}
//       </Slider>
//     </div>
//   );
// };

// export default FeatureCarousel;



// import React, { useEffect, useRef } from "react";
// import "./FeatureCarousel.css";

// const features = [
//   { icon: "📇", title: "Access to 700M+ Contacts" },
//   { icon: "🌐", title: "Supports 9 Global Languages" },
//   { icon: "🤖", title: "Advanced AI Productivity" },
//   { icon: "📤", title: "Bulk Export & Enrich" },
//   { icon: "🤝", title: "Seamless Deal Management" },
//   { icon: "📧", title: "Instant Email Finder" },
//   { icon: "📊", title: "Advanced Waterfall Enrichment" },
// ];

// const FeatureCarousel = () => {
//   const carouselRef = useRef(null);

//   useEffect(() => {
//     const carousel = carouselRef.current;
//     const step = 2; 
//     const delay = 20; 

//     const scrollInterval = setInterval(() => {
//       if (carousel) {
//         carousel.scrollLeft -= step; 

//         if (carousel.scrollLeft <= 0) {
//           carousel.scrollLeft = carousel.scrollWidth / 2;
//         }
//       }
//     }, delay);

//     return () => clearInterval(scrollInterval);
//   }, []);

//   return (
//     <div className="carousel-wrapper">
//       <div className="carousel" ref={carouselRef}>
//         {features.map((feature, index) => (
//           <div className="carousel-item" key={index}>
//             <div className="icon">{feature.icon}</div>
//             <p>{feature.title}</p>
//           </div>
//         ))}
//         {features.map((feature, index) => (
//           <div className="carousel-item" key={`dup-${index}`}>
//             <div className="icon">{feature.icon}</div>
//             <p>{feature.title}</p>
//           </div>
//         ))}
//       </div>
//     </div>
//   );
// };

// export default FeatureCarousel;



import React from "react";
import "./FeatureCarousel.css";

// Image import
import carousel1 from "../../assets/carousel1.png";
import carousel2 from "../../assets/carousel2.png";
import carousel3 from "../../assets/carousel3.png";
import carousel4 from "../../assets/carousel4.png";

const features = [
  { icon: carousel1, title: "Access to 700M+ Contacts" },  
  { icon: carousel2, title: "One click push to CRM" }, 
  { icon: carousel3, title: "Custom Field Mapping" }, 
  { icon: carousel4, title: "Advanced Waterfall Enrichment" },
];

// Duplicate list for infinite loop
const loopItems = [...features, ...features];

function FeatureCarousel() {
  return (
    <div className="fc">
      <div className="fc-track">
        {loopItems.map((f, i) => (
          <div className="fc-item" key={i}>
            <div className="fc-icon">
              {typeof f.icon === "string" && f.icon.includes("/") ? (
                <img src={f.icon} alt="feature" />
              ) : (
                <span>{f.icon}</span>
              )}
            </div>
            <span className="fc-text">{f.title}</span>
          </div>
        ))}
      </div>
    </div>
  );
}

export default FeatureCarousel;

