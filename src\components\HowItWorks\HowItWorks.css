.how-it-works {
  padding: 40px 20px;
  text-align: center;
  background-color: #f9f9f9;
}

.how-it-works h2 {
  font-size: 2rem;
  margin-bottom: 10px;
}

.how-it-works p {
  font-size: 1.1rem;
  margin-bottom: 30px;
}

.steps-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px;
}

.step {
  background: white;
  padding: 20px;
  border-radius: 10px;
  flex: 1 1 250px;
  max-width: 300px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.step-number {
  font-size: 2rem;
  font-weight: bold;
  color: #00aaff;
  margin-bottom: 15px;
}

.step img {
  width: 100%;
  margin-top: 15px;
  border-radius: 5px;
}

.try-now {
  margin-top: 30px;
  background-color: #7dfc36;
  color: black;
  padding: 12px 24px;
  font-size: 1rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

@media (max-width: 768px) {
  .steps-container {
    flex-direction: column;
    align-items: center;
  }
}
