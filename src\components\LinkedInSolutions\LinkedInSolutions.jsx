import React from "react";
import "./LinkedInSolutions.css";
import image1 from "../../assets/box1_1.jpeg";
import image2 from "../../assets/box1_right.png";
import image3 from "../../assets/box2.jpeg";
import icon1 from "../../assets/linkedinicon1.png";
import icon2 from "../../assets/linkedinicon2.png";
import icon3 from "../../assets/linkedinicon3.png";
import icon4 from "../../assets/linkedinicon4.png";

const LinkedInSolutions = () => {
  return (
    <div className="linkedin-container">
      <h1 className="main-heading">Complete LinkedIn Sales Solutions</h1>
      <p className="sub-heading">
        Everything you need for professional LinkedIn prospecting
      </p>

      {/* Feature Tabs */}
      <div className="features-tabs">
        <div className="feature-item active">
          <img src={icon1} alt="CRM Enrichment" />
          <span>CRM Data Enrichment</span>
        </div>
        <div className="feature-item">
          <img src={icon2} alt="CRM Data Sync" />
          <span>CRM Data Sync</span>
        </div>
        <div className="feature-item">
          <img src={icon3} alt="Bulk Export" />
          <span>Bulk Export & Enrichment</span>
        </div>
        <div className="feature-item">
          <img src={icon4} alt="AI Productivity" />
          <span>AI Productivity</span>
        </div>
      </div>

      {/* Bot Icon */}
      {/* <div className="image-title-section">
        <img src="/icons/bot-icon.png" alt="Bot Icon" className="bot-icon" />
      </div> */}

      {/* Info text */}
      <p className="info-text">
        It’s hard to find the accurate contact data for every prospects by{" "}
        <span className="incomplete-data">Incomplete Data</span>
      </p>

      {/* Content Boxes */}
      <div className="boxes-container">
        {/* Left Box */}
        <div className="box left-box">
          <h3>Here is how LeadCRM tackles that situation.</h3>
          <p className="box-subheading green-link">
            Try LeadCRM Data Enrichment&nbsp; &gt;
          </p>
          <div className="images two-images">
            <img
              src={image1}
              alt="LeadCRM Verified Info"
              className="box-image"
            />
            <img src={image2} alt="Search Filters" className="box-image" />
          </div>
        </div>

        {/* Right Box */}
        <div className="box right-box">
          <h3>Capture Every Lead. Every Time.</h3>
          <p className="box-subheading green-link">
            Try Our Advanced Waterfall Enrichment&nbsp; &gt;
          </p>
          {/* <h3>
            If it does not work for you ! try our{" "}
            <span className="green-text">Advanced Waterfall Enrichment</span>{" "}
            &nbsp; &gt;
          </h3> */}
          <div className="waterfall-image">
            <img src={image3} alt="Waterfall Enrichment" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default LinkedInSolutions;
