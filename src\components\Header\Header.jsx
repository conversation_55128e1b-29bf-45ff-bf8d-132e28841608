// import React, { useState } from "react";
// import "./Header.css";

// const Header = () => {
//   const [menuOpen, setMenuOpen] = useState(false);

//   return (
//     <header className="header">
//       <div className="logo">LEADCRM.IO</div>

//       <nav className={`nav ${menuOpen ? "open" : ""}`}>
//         <ul>
//           <li><a href="#product">Product</a></li>
//           <li><a href="#pricing">Pricing</a></li>
//           <li><a href="#resources">Resources</a></li>
//           <li><a href="#company">Company</a></li>
//         </ul>
//       </nav>

//       <div className="header-buttons">
//         <button className="btn-outline">Login</button>
//         <button className="btn-primary">Get your free account</button>
//       </div>

//       <div className="menu-toggle" onClick={() => setMenuOpen(!menuOpen)}>
//         ☰
//       </div>
//     </header>
//   );
// };

// export default Header;


// import React, { useState } from "react";
// import "./Header.css";

// import { FaNewspaper, FaDatabase, FaExchangeAlt, FaProjectDiagram, FaFileExport, FaQuestionCircle } from "react-icons/fa";
// import { FaUserFriends, FaUsers, FaFolderOpen, FaLink, FaCommentDots, FaRobot, FaBolt } from "react-icons/fa";
// import { FaChrome, FaFirefoxBrowser, FaEdge } from "react-icons/fa";

// const Header = () => {
//   const [menuOpen, setMenuOpen] = useState(false);
//   const [activeDropdown, setActiveDropdown] = useState(null);

//   return (
//     <header className="header">
//       {/* Logo */}
//       <div className="logo">LEADCRM.IO</div>

//       {/* Navigation */}
//       <nav className={`nav ${menuOpen ? "open" : ""}`}>
//         <ul>
//           {/* Product dropdown */}
//           <li
//             className="dropdown"
//             onMouseEnter={() => setActiveDropdown("product")}
//             onMouseLeave={() => setActiveDropdown(null)}
//           >
//             <a href="#product">Product ▾</a>
//             {activeDropdown === "product" && (
//               <div className="dropdown-product">
//                 <div className="product-left">
//                   <h3>LinkedIn Extension</h3>
//                   <p>
//                     Easily manage your network, organize leads, and integrate seamlessly
//                     with your CRM for efficient follow-ups and enhanced sales workflows.
//                   </p>
//                   <img
//                     src="https://via.placeholder.com/220x120.png?text=Product+Preview"
//                     alt="LinkedIn Extension"
//                   />
//                 </div>

//                 <div className="product-center">
//                   <h3>Features</h3>
//                   <ul>
//                     <li><FaUserFriends /> Lead Finder – Professional Profiles Instantly</li>
//                     <li><FaUsers /> Teammate – Empower. Collaborate. Grow.</li>
//                     <li><FaFolderOpen /> Deal Management – Streamline Your Deal Management</li>
//                     <li><FaLink /> Waterfall Data Enrichment – Transform Data. Drive Success.</li>
//                     <li><FaFileExport /> Bulk Export & Enrich – Export & Auto-Enrich</li>
//                     <li><FaDatabase /> CRM Data Sync – Sync LinkedIn Contacts</li>
//                     <li><FaProjectDiagram /> CRM Data Overlay – Intel Overlaid on CRM</li>
//                     <li><FaCommentDots /> AI-Assisted Commenting – Build Trust</li>
//                     <li><FaRobot /> AI Response Assistant – Reply Smarter</li>
//                     <li><FaBolt /> Templates & Shortcuts – 1-Click Templates</li>
//                   </ul>
//                 </div>

//                 <div className="product-right">
//                   <h3>LeadCRM Integration</h3>
//                   <p><FaLink /> CRM Integration – Boost Productivity with Integration</p>

//                   <h3>Extension</h3>
//                   <ul>
//                     <li><FaChrome /> Chrome – LinkedIn Chrome Extension</li>
//                     <li><FaFirefoxBrowser /> Firefox – Coming Soon</li>
//                     <li><FaEdge /> Edge – Coming Soon</li>
//                   </ul>
//                 </div>
//               </div>
//             )}
//           </li>

//           <li><a href="#pricing">Pricing</a></li>

//           {/* Resources dropdown */}
//           <li
//             className="dropdown"
//             onMouseEnter={() => setActiveDropdown("resources")}
//             onMouseLeave={() => setActiveDropdown(null)}
//           >
//             <a href="#resources">Resources ▾</a>
//             {activeDropdown === "resources" && (
//               <ul className="dropdown-resources">
//                 <li><FaNewspaper /> Blogs</li>
//                 <li><FaDatabase /> API Documentation</li>
//                 <li><FaExchangeAlt /> LeadCRM Alternatives</li>
//                 <li><FaProjectDiagram /> LinkedIn CRM Integration Guide</li>
//                 <li><FaFileExport /> Lead Export Guide</li>
//                 <li><FaQuestionCircle /> FAQs</li>
//               </ul>
//             )}
//           </li>

//           {/* Company dropdown */}
//           <li
//             className="dropdown"
//             onMouseEnter={() => setActiveDropdown("company")}
//             onMouseLeave={() => setActiveDropdown(null)}
//           >
//             <a href="#company">Company ▾</a>
//             {activeDropdown === "company" && (
//               <ul className="dropdown-company">
//                 <li>ℹ️ About Us</li>
//                 <li>📞 Contact Us</li>
//               </ul>
//             )}
//           </li>
//         </ul>
//       </nav>

//       {/* Right buttons */}
//       <div className="header-buttons">
//         <button className="btn-outline">Login</button>
//         <button className="btn-primary">Get your free account</button>
//       </div>

//       {/* Mobile Menu Button */}
//       <div className="menu-toggle" onClick={() => setMenuOpen(!menuOpen)}>
//         ☰
//       </div>
//     </header>
//   );
// };

// export default Header;



import React from "react";
import "./Header.css";
import logo from '../../assets/Leadcrm Logo.png'

const Header = () => {
  return (
    <header className="header">
      <div className="logo">
        <img src={logo} alt="LeadCRM Logo" />
      </div>

      <nav className="nav-links">
        <div className="dropdown">
          <button className="dropbtn">Product ▼</button>
          <div className="dropdown-content">
            <a href="#">Features</a>
            <a href="#">Integrations</a>
            <a href="#">API</a>
          </div>
        </div>

        <a href="#">Pricing</a>

        <div className="dropdown">
          <button className="dropbtn">Resources ▼</button>
          <div className="dropdown-content">
            <a href="#">Blog</a>
            <a href="#">Docs</a>
            <a href="#">Help Center</a>
          </div>
        </div>

        <div className="dropdown">
          <button className="dropbtn">Company ▼</button>
          <div className="dropdown-content">
            <a href="#">About Us</a>
            <a href="#">Careers</a>
            <a href="#">Contact</a>
          </div>
        </div>
      </nav>

      <div className="auth-buttons">
        <button className="free-account">Get your free account</button>
        <button className="login">Login</button>
      </div>
    </header>
  );
};

export default Header;
