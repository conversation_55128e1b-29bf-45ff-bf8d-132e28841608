import React, { useState, useEffect } from "react";
import "./Header.css";
import logo from '../../assets/Leadcrm Logo.png'

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState(null);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    if (isMenuOpen) {
      document.body.classList.add('menu-open');
    } else {
      document.body.classList.remove('menu-open');
    }

    return () => document.body.classList.remove('menu-open');
  }, [isMenuOpen]);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
    if (!isMenuOpen) {
      setActiveDropdown(null);
    }
  };

  const toggleDropdown = (dropdown) => {
    if (isMobile) {
      setActiveDropdown(activeDropdown === dropdown ? null : dropdown);
    }
  };

  const handleMouseEnter = (dropdown) => {
    if (!isMobile) {
      setActiveDropdown(dropdown);
    }
  };

  const handleMouseLeave = () => {
    if (!isMobile) {
      setActiveDropdown(null);
    }
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
    setActiveDropdown(null);
  };

  return (
    <header className="header">
      <div className="header-container">
        <div className="logo">
          <img src={logo} alt="LeadCRM Logo" />
        </div>

        {/* Navigation */}
        <nav className={`nav-links ${isMenuOpen ? 'nav-open' : ''}`}>
          <div
            className="dropdown"
            onMouseEnter={() => handleMouseEnter('product')}
            onMouseLeave={handleMouseLeave}
          >
            <button
              className="dropbtn"
              onClick={() => toggleDropdown('product')}
            >
              Product ▼
            </button>
            <div className={`dropdown-content ${activeDropdown === 'product' ? 'show' : ''}`}>
              <a href="#" onClick={closeMenu}>Features</a>
              <a href="#" onClick={closeMenu}>Integrations</a>
              <a href="#" onClick={closeMenu}>API</a>
            </div>
          </div>

          <a href="#" onClick={closeMenu}>Pricing</a>

          <div
            className="dropdown"
            onMouseEnter={() => handleMouseEnter('resources')}
            onMouseLeave={handleMouseLeave}
          >
            <button
              className="dropbtn"
              onClick={() => toggleDropdown('resources')}
            >
              Resources ▼
            </button>
            <div className={`dropdown-content ${activeDropdown === 'resources' ? 'show' : ''}`}>
              <a href="#" onClick={closeMenu}>Blog</a>
              <a href="#" onClick={closeMenu}>Docs</a>
              <a href="#" onClick={closeMenu}>Help Center</a>
            </div>
          </div>

          <div
            className="dropdown"
            onMouseEnter={() => handleMouseEnter('company')}
            onMouseLeave={handleMouseLeave}
          >
            <button
              className="dropbtn"
              onClick={() => toggleDropdown('company')}
            >
              Company ▼
            </button>
            <div className={`dropdown-content ${activeDropdown === 'company' ? 'show' : ''}`}>
              <a href="#" onClick={closeMenu}>About Us</a>
              <a href="#" onClick={closeMenu}>Careers</a>
              <a href="#" onClick={closeMenu}>Contact</a>
            </div>
          </div>

          {/* Mobile Auth Buttons */}
          <div className="mobile-auth-buttons">
            <button className="free-account" onClick={closeMenu}>Get your free account</button>
            <button className="login" onClick={closeMenu}>Login</button>
          </div>
        </nav>

        {/* Desktop Auth Buttons */}
        <div className="auth-buttons desktop-auth">
          <button className="free-account">Get your free account</button>
          <button className="login">Login</button>
        </div>

        {/* Mobile Menu Toggle */}
        <button
          className={`menu-toggle ${isMenuOpen ? 'active' : ''}`}
          onClick={toggleMenu}
          aria-label="Toggle menu"
        >
          <span className="hamburger-line"></span>
          <span className="hamburger-line"></span>
          <span className="hamburger-line"></span>
        </button>
      </div>

      {/* Mobile Menu Overlay */}
      <div className={`menu-overlay ${isMenuOpen ? 'active' : ''}`} onClick={closeMenu}></div>
    </header>
  );
};

export default Header;
