/* Hero Component Styles */
.hero-section {
  background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 50%, #81d4fa 100%);
  padding: 2rem 0 4rem;
  position: relative;
  overflow: hidden;
  min-height: 80vh;
  display: flex;
  align-items: center;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
}

/* Top Tagline */
.hero-tagline {
  text-align: center;
  margin-bottom: 2rem;
}

.hero-tagline p {
  font-size: 1.25rem;
  color: #1e293b;
  font-weight: 500;
  margin: 0;
}

/* Hero Content */
.hero-content {
  margin-bottom: 3rem;
}

.hero-banner {
  background: #1e88e5;
  border-radius: 20px;
  padding: 2rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(30, 136, 229, 0.3);
}

.banner-header {
  text-align: center;
  margin-bottom: 2rem;
}

.banner-title {
  color: white;
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
  line-height: 1.2;
}

/* Integration Badges */
.integration-badges {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.badge {
  background: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;
  height: 40px;
}

.badge-logo {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* Dashboard Showcase */
.dashboard-showcase {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1rem;
  margin-top: 2rem;
}

.dashboard-item {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
}

.dashboard-item:hover {
  transform: translateY(-5px);
}

.dashboard-window {
  height: 200px;
  display: flex;
  flex-direction: column;
}

.window-header {
  background: #f1f5f9;
  padding: 0.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.window-controls {
  display: flex;
  gap: 0.25rem;
}

.control {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.control.red { background: #ef4444; }
.control.yellow { background: #f59e0b; }
.control.green { background: #10b981; }

.window-content {
  flex: 1;
  padding: 0.75rem;
  overflow: hidden;
}

/* LinkedIn Profile Interface */
.linkedin-profile {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.profile-header {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.profile-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(45deg, #0077b5, #00a0dc);
}

.profile-info {
  flex: 1;
}

.profile-name {
  height: 12px;
  background: #cbd5e1;
  border-radius: 4px;
  margin-bottom: 0.25rem;
}

.profile-title {
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  width: 80%;
}

.profile-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: auto;
}

.action-btn {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  text-align: center;
  flex: 1;
}

.action-btn.primary {
  background: #0077b5;
  color: white;
}

.action-btn.secondary {
  background: #f1f5f9;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

/* CRM Interface */
.crm-interface {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.crm-header {
  margin-bottom: 0.75rem;
}

.crm-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1e293b;
}

.crm-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.crm-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: #f8fafc;
  border-radius: 6px;
}

.item-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(45deg, #3b82f6, #1d4ed8);
}

.item-info {
  flex: 1;
}

.item-name {
  height: 8px;
  background: #cbd5e1;
  border-radius: 4px;
  margin-bottom: 0.25rem;
}

.item-company {
  height: 6px;
  background: #e2e8f0;
  border-radius: 4px;
  width: 70%;
}

.item-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.item-status.active { background: #10b981; }
.item-status.pending { background: #f59e0b; }

/* Sync Interface */
.sync-interface {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sync-header {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.75rem;
}

.sync-progress {
  margin-bottom: 0.75rem;
}

.progress-bar {
  height: 4px;
  background: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 0.25rem;
}

.progress-fill {
  height: 100%;
  width: 65%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 2px;
  animation: progress 2s ease-in-out infinite;
}

.sync-status {
  font-size: 0.75rem;
  color: #64748b;
}

.sync-items {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.sync-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #64748b;
}

.sync-icon {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #e2e8f0;
}

.sync-item.completed .sync-icon {
  background: #10b981;
}

.sync-item.active .sync-icon {
  background: #3b82f6;
  animation: pulse 1s ease-in-out infinite;
}

/* Analytics Interface */
.analytics-interface {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.analytics-header {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.75rem;
}

.analytics-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 1rem;
  font-weight: 700;
  color: #1e293b;
}

.stat-label {
  font-size: 0.625rem;
  color: #64748b;
}

.analytics-chart {
  flex: 1;
  display: flex;
  align-items: end;
}

.chart-bars {
  display: flex;
  gap: 0.25rem;
  align-items: end;
  width: 100%;
  height: 60px;
}

.chart-bar {
  flex: 1;
  background: linear-gradient(to top, #3b82f6, #1d4ed8);
  border-radius: 2px 2px 0 0;
  min-height: 20%;
}

/* Notification Interface */
.notification-interface {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.notification-header {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.75rem;
}

.notification-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.notification-item {
  display: flex;
  gap: 0.5rem;
  align-items: flex-start;
}

.notification-icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.notification-icon.success { background: #10b981; }
.notification-icon.info { background: #3b82f6; }

.notification-text {
  flex: 1;
}

.notification-title {
  font-size: 0.75rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.125rem;
}

.notification-desc {
  font-size: 0.625rem;
  color: #64748b;
}

/* Call to Action */
.hero-cta {
  text-align: center;
}

.cta-button {
  background: linear-gradient(135deg, #84cc16, #65a30d);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(132, 204, 22, 0.4);
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(132, 204, 22, 0.5);
}

.cta-arrow {
  transition: transform 0.3s ease;
}

.cta-button:hover .cta-arrow {
  transform: translateX(4px);
}

/* Background Elements */
.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.bg-gradient {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 30% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(132, 204, 22, 0.1) 0%, transparent 50%);
}

.bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* Animations */
@keyframes progress {
  0%, 100% { transform: translateX(0); }
  50% { transform: translateX(10px); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-section {
    padding: 1.5rem 0 3rem;
    min-height: 70vh;
  }
  
  .hero-container {
    padding: 0 1.5rem;
  }
  
  .banner-title {
    font-size: 2rem;
  }
  
  .dashboard-showcase {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .dashboard-item:nth-child(4),
  .dashboard-item:nth-child(5) {
    grid-column: span 1;
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding: 1rem 0 2rem;
    min-height: 60vh;
  }
  
  .hero-container {
    padding: 0 1rem;
  }
  
  .hero-tagline p {
    font-size: 1.125rem;
  }
  
  .banner-title {
    font-size: 1.75rem;
  }
  
  .hero-banner {
    padding: 1.5rem;
  }
  
  .integration-badges {
    gap: 0.5rem;
  }
  
  .badge {
    min-width: 70px;
    height: 35px;
    padding: 0.375rem 0.75rem;
  }
  
  .dashboard-showcase {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }
  
  .dashboard-window {
    height: 180px;
  }
  
  .cta-button {
    padding: 0.875rem 1.75rem;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 0.75rem 0 1.5rem;
    min-height: 50vh;
  }
  
  .hero-container {
    padding: 0 0.75rem;
  }
  
  .hero-tagline {
    margin-bottom: 1.5rem;
  }
  
  .hero-tagline p {
    font-size: 1rem;
  }
  
  .banner-title {
    font-size: 1.5rem;
  }
  
  .hero-banner {
    padding: 1rem;
    border-radius: 16px;
  }
  
  .integration-badges {
    gap: 0.375rem;
    margin-bottom: 1.5rem;
  }
  
  .badge {
    min-width: 60px;
    height: 30px;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }
  
  .dashboard-showcase {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .dashboard-window {
    height: 160px;
  }
  
  .hero-content {
    margin-bottom: 2rem;
  }
  
  .cta-button {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .dashboard-item,
  .cta-button,
  .cta-arrow,
  .progress-fill,
  .sync-icon {
    transition: none;
    animation: none;
  }
}

/* Focus states */
.cta-button:focus {
  outline: 2px solid #84cc16;
  outline-offset: 2px;
}

.cta-button:focus-visible {
  outline: 2px solid #84cc16;
  outline-offset: 2px;
}
