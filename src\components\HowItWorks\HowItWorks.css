/* .howitworks {
  background: #f5f8ff;
  padding: 60px 20px;
  text-align: center;
}

.title {
  font-size: 28px;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 40px;
}

.title span {
  display: block;
  font-size: 16px;
  font-weight: 400;
  color: #555;
  margin-top: 5px;
}

.steps-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr); 
  gap: 24px;
}

.step-card {
  background: #fff;
  border-radius: 14px;
  padding: 24px;
  box-shadow: 0px 4px 15px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s, box-shadow 0.3s;
  text-align: left;
}

.step-card:hover {
  transform: translateY(-6px);
  box-shadow: 0px 8px 20px rgba(0, 0, 0, 0.12);
}

.step-number {
  font-size: 36px;
  font-weight: 700;
  color: #0a66c2;
  margin-bottom: 12px;
}

.step-card h3 {
  font-size: 18px;
  margin-bottom: 10px;
  color: #222;
}

.step-card p {
  font-size: 14px;
  color: #555;
  margin-bottom: 16px;
  line-height: 1.4;
}

.step-card img {
  width: 100%;
  border-radius: 8px;
  margin-top: 12px;
}

.cta {
  margin-top: 40px;
}

.btn-primary {
  background: #0a66c2;
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.3s;
}

.btn-primary:hover {
  background: #084a90;
}

@media (max-width: 1200px) {
  .steps-container {
    grid-template-columns: repeat(2, 1fr); 
  }
}

@media (max-width: 600px) {
  .steps-container {
    grid-template-columns: 1fr; 
  }
} */




.how-it-works {
  padding: 40px 20px;
  text-align: center;
  background-color: #f9f9f9;
}

.how-it-works h2 {
  font-size: 2rem;
  margin-bottom: 10px;
}

.how-it-works p {
  font-size: 1.1rem;
  margin-bottom: 30px;
}

.steps-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px;
}

.step {
  background: white;
  padding: 20px;
  border-radius: 10px;
  flex: 1 1 250px;
  max-width: 300px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.step-number {
  font-size: 2rem;
  font-weight: bold;
  color: #00aaff;
  margin-bottom: 15px;
}

.step img {
  width: 100%;
  margin-top: 15px;
  border-radius: 5px;
}

.try-now {
  margin-top: 30px;
  background-color: #7dfc36;
  color: black;
  padding: 12px 24px;
  font-size: 1rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

@media (max-width: 768px) {
  .steps-container {
    flex-direction: column;
    align-items: center;
  }
}
