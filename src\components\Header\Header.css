.header {
  position: relative;
  background-color: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.logo img {
  height: 40px;
  width: auto;
}

/* Desktop Navigation */
.nav-links {
  display: flex;
  gap: 20px;
  align-items: center;
}

.nav-links a, .dropbtn {
  text-decoration: none;
  color: #1a2c5b;
  font-weight: 500;
  cursor: pointer;
  background: none;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.nav-links a:hover, .dropbtn:hover {
  background-color: #f8f9fa;
  color: #0f3c82;
}

.dropdown {
  position: relative;
}

.dropdown-content {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  background-color: white;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  min-width: 180px;
  border-radius: 8px;
  padding: 8px 0;
  z-index: 1001;
}

.dropdown-content a {
  display: block;
  padding: 12px 16px;
  color: #333;
  border-radius: 0;
  margin: 0;
}

.dropdown-content a:hover {
  background-color: #f8f9fa;
  color: #0f3c82;
}

/* Desktop hover behavior */
@media (min-width: 769px) {
  .dropdown:hover .dropdown-content {
    display: block;
  }
}

/* Show dropdown when active */
.dropdown-content.show {
  display: block;
}

/* Auth Buttons */
.auth-buttons {
  display: flex;
  gap: 15px;
  align-items: center;
}

.desktop-auth {
  display: flex;
}

.mobile-auth-buttons {
  display: none;
  flex-direction: column;
  gap: 15px;
  width: 100%;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.free-account {
  background-color: #0f3c82;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.free-account:hover {
  background-color: #0d3470;
}

.login {
  background: none;
  border: 2px solid #0f3c82;
  padding: 8px 20px;
  border-radius: 6px;
  color: #0f3c82;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.login:hover {
  background-color: #0f3c82;
  color: white;
}

/* Mobile Menu Toggle */
.menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: space-around;
  width: 30px;
  height: 30px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: 1002;
}

.hamburger-line {
  width: 100%;
  height: 3px;
  background-color: #1a2c5b;
  border-radius: 2px;
  transition: all 0.3s ease;
  transform-origin: center;
}

.menu-toggle.active .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.menu-toggle.active .hamburger-line:nth-child(2) {
  opacity: 0;
}

.menu-toggle.active .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Menu Overlay */
.menu-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .header-container {
    padding: 15px 20px;
  }

  .menu-toggle {
    display: flex;
  }

  .desktop-auth {
    display: none;
  }

  .mobile-auth-buttons {
    display: flex;
  }

  .menu-overlay {
    display: block;
  }

  .nav-links {
    position: fixed;
    top: 0;
    right: -100%;
    width: 280px;
    height: 100vh;
    background-color: white;
    flex-direction: column;
    align-items: flex-start;
    padding: 80px 30px 30px;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    transition: right 0.3s ease;
    z-index: 1001;
    overflow-y: auto;
  }

  .nav-links.nav-open {
    right: 0;
  }

  .nav-links a, .dropbtn {
    width: 100%;
    text-align: left;
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
    font-size: 16px;
  }

  .dropdown {
    width: 100%;
  }

  .dropdown-content {
    position: static;
    display: none;
    box-shadow: none;
    background-color: #f8f9fa;
    margin-left: 20px;
    border-radius: 4px;
    margin-top: 10px;
  }

  .dropdown-content a {
    padding: 10px 15px;
    font-size: 14px;
    border-bottom: none;
  }

  .mobile-auth-buttons .free-account,
  .mobile-auth-buttons .login {
    width: 100%;
    text-align: center;
    padding: 12px 20px;
  }
}

@media (max-width: 480px) {
  .header-container {
    padding: 12px 15px;
  }

  .logo img {
    height: 35px;
  }

  .nav-links {
    width: 100%;
    right: -100%;
  }

  .nav-links.nav-open {
    right: 0;
  }
}
