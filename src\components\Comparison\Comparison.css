/* .comparison {
  text-align: center;
  padding: 60px 20px;
  background: #fff;
}

.comparison-title {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 40px;
  color: #222;
}

.comparison-container {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.card {
  flex: 1;
  min-width: 300px;
  max-width: 480px;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0px 4px 12px rgba(0,0,0,0.08);
  text-align: left;
  transition: transform 0.3s;
  background: #fff;
}

.card:hover {
  transform: translateY(-4px);
}

.card h3 {
  font-size: 20px;
  margin-bottom: 20px;
  font-weight: 700;
}

.card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.card ul li {
  margin-bottom: 14px;
  font-size: 15px;
  line-height: 1.5;
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.icon {
  font-size: 18px;
  margin-top: 3px;
}

.red { color: #e63946; }
.green { color: #2a9d8f; }

.without {
  border-left: 5px solid #e63946;
}

.with {
  border-left: 5px solid #2a9d8f;
}

.cta {
  margin-top: 40px;
}

.btn-primary {
  background: #0a66c2;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.3s;
}

.btn-primary:hover {
  background: #084a90;
}

.save-text {
  margin-top: 12px;
  font-size: 14px;
  color: #2a9d8f;
  font-weight: 500;
  font-style: italic;
}

@media (max-width: 900px) {
  .comparison-container {
    flex-direction: column;
    align-items: center;
  }
  .card {
    width: 100%;
    max-width: 600px;
  }
} */


/* .comparison-section {
  padding: 40px 20px;
  text-align: center;
}

.comparison-section h2 {
  font-size: 1.8rem;
  margin-bottom: 40px;
}

.comparison-cards {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  flex: 1 1 300px;
  max-width: 400px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  text-align: left;
}

.card-image {
  text-align: center;
  margin-bottom: 15px;
}

.card-image img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
}

.without-leadcrm {
  border: 1px solid #f5c6cb;
}

.with-leadcrm {
  border: 1px solid #c3e6cb;
}

.vs-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.5rem;
}

.card h3 {
  font-size: 1.3rem;
  margin-bottom: 15px;
}

.card ul {
  list-style: none;
  padding: 0;
}

.card ul li {
  margin-bottom: 15px;
}

.cta-button {
  margin-top: 30px;
  background-color: #7dfc36;
  color: black;
  padding: 12px 24px;
  font-size: 1rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.small-note {
  margin-top: 10px;
  font-style: italic;
  color: #555;
}

@media (max-width: 768px) {
  .comparison-cards {
    flex-direction: column;
    align-items: center;
  }

  .vs-divider {
    display: none;
  }
} */


.comparison-section {
  padding: 60px 20px;
  text-align: center;
}

.comparison-section h2 {
  font-size: 2rem;
  margin-bottom: 50px;
}

.comparison-cards {
  display: flex;
  justify-content: center;
  gap: 50px;
  flex-wrap: wrap;
  align-items: flex-start;
}

.card {
  background: white;
  padding: 25px;
  border-radius: 12px;
  flex: 1 1 300px;
  max-width: 400px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
  text-align: left;
}

.card-image {
  text-align: center;
  margin-bottom: 20px;
}

.card-image img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
}

.card h3 {
  font-size: 1.5rem;
  margin-bottom: 20px;
}

.card ul {
  list-style: none;
  padding: 0;
}

.card ul li {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 20px;
}

.card ul li .icon {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.cta-button {
  margin-top: 40px;
  background-color: #7dfc36;
  color: black;
  padding: 14px 30px;
  font-size: 1.1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}

.small-note {
  margin-top: 12px;
  font-style: italic;
  color: #555;
}

.vs-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 2rem;
  color: #888;
}

@media (max-width: 900px) {
  .comparison-cards {
    flex-direction: column;
    align-items: center;
  }

  .vs-divider {
    display: none;
  }
}
