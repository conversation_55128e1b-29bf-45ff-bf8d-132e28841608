.footer {
  background: #1f2937;
  color: #ffffff;
  padding: 80px 0 0;
}

.footer__content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 80px;
  margin-bottom: 60px;
}

/* Footer Brand */
.footer__brand {
  max-width: 350px;
}

.footer__logo {
  margin-bottom: 24px;
}

.footer__logo .logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.footer__logo .logo__text {
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
}

.footer__description {
  color: #d1d5db;
  line-height: 1.6;
  margin-bottom: 32px;
}

.footer__social {
  display: flex;
  gap: 16px;
}

.social-link {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #d1d5db;
  transition: all 0.3s ease;
  text-decoration: none;
}

.social-link:hover {
  background: #0066cc;
  color: #ffffff;
  transform: translateY(-2px);
}

/* Footer Links */
.footer__links {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40px;
}

.footer__column-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 20px;
}

.footer__list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer__list li {
  margin-bottom: 12px;
}

.footer__link {
  color: #d1d5db;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer__link:hover {
  color: #ffffff;
}

/* Footer Bottom */
.footer__bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32px 0;
  border-top: 1px solid #374151;
}

.footer__copyright {
  color: #9ca3af;
  font-size: 14px;
}

.footer__badges {
  display: flex;
  gap: 24px;
}

.badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  font-size: 12px;
  color: #d1d5db;
}

.badge__icon {
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .footer__content {
    grid-template-columns: 1fr;
    gap: 60px;
  }
  
  .footer__links {
    grid-template-columns: repeat(2, 1fr);
    gap: 32px;
  }
}

@media (max-width: 768px) {
  .footer {
    padding: 60px 0 0;
  }
  
  .footer__content {
    gap: 40px;
    margin-bottom: 40px;
  }
  
  .footer__links {
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
  }
  
  .footer__bottom {
    flex-direction: column;
    gap: 20px;
    text-align: center;
    padding: 24px 0;
  }
  
  .footer__badges {
    flex-wrap: wrap;
    justify-content: center;
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .footer__links {
    grid-template-columns: 1fr;
    gap: 32px;
  }
  
  .footer__brand {
    text-align: center;
  }
  
  .footer__social {
    justify-content: center;
  }
  
  .footer__badges {
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }
  
  .badge {
    justify-content: center;
    min-width: 140px;
  }
}
