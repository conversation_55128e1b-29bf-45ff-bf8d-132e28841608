import React from 'react';
import './Testimonials.css';
import testimonial1 from '../../assets/testimonial1.png';
import testimonial2 from '../../assets/testimonial2.png';
import testimonial3 from '../../assets/testimonial3.png';

const Testimonials = () => {
  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      role: "Co-Founder",
      rating: 5,
      text: "It's been a really positive experience overall. I was skeptical to build qualified leads from LinkedIn and sync them into HubSpot. The platform has saved me hours of manual prospecting and has helped me build a better relationship with my customers and prospects - they are both knowledgeable and genuinely helpful.",
      avatar: {testimonial1},
      verified: true
    },
    {
      id: 2,
      name: "<PERSON>",
      role: "Sales Manager",
      rating: 5,
      text: "Simply life changing! I never kept track of a sales rep that I can see and engage leads right in LinkedIn. The result performance is excellent, and when it makes anything, it's always high quality.",
      avatar: ,
      verified: true
    },
    {
      id: 3,
      name: "<PERSON><PERSON>",
      role: "Marketing Director",
      rating: 5,
      text: "Number 1 integration is Great! I love that LeadCRM keeps multiple sources, including in one. If one source doesn't have the data, the next one will. Message templates help me save time and increase response.",
      avatar: "/api/placeholder/40/40",
      verified: true
    }
  ];

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, index) => (
      <span
        key={index}
        className={`star ${index < rating ? 'filled' : 'empty'}`}
      >
        ★
      </span>
    ));
  };

  return (
    <section className="testimonials-section">
      <div className="testimonials-container">
        {/* Header */}
        <div className="testimonials-header">
          <h2 className="testimonials-title">See Why Top Performers Choose LeadCRM</h2>
        </div>

        {/* Testimonials Grid */}
        <div className="testimonials-grid">
          {testimonials.map((testimonial) => (
            <div key={testimonial.id} className="testimonial-card">
              <div className="testimonial-content">
                <div className="testimonial-text">
                  <p>"{testimonial.text}"</p>
                </div>
                
                <div className="testimonial-rating">
                  <div className="stars">
                    {renderStars(testimonial.rating)}
                  </div>
                </div>

                <div className="testimonial-author">
                  <div className="author-info">
                    <div className="author-avatar">
                      <img 
                        src={testimonial.avatar} 
                        alt={testimonial.name}
                        className="avatar-img"
                      />
                      {testimonial.verified && (
                        <div className="verified-badge">
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" strokeWidth="2" fill="none"/>
                          </svg>
                        </div>
                      )}
                    </div>
                    <div className="author-details">
                      <h4 className="author-name">{testimonial.name}</h4>
                      <p className="author-role">{testimonial.role}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Background Decorations */}
        <div className="testimonials-decorations">
          <div className="decoration-circle circle-1"></div>
          <div className="decoration-circle circle-2"></div>
          <div className="decoration-circle circle-3"></div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
