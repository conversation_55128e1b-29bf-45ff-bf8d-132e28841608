import React, { useState } from "react";
import "./Header.css";
import logo from '../../assets/Leadcrm Logo.png'

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState(null);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const toggleDropdown = (dropdown) => {
    setActiveDropdown(activeDropdown === dropdown ? null : dropdown);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
    setActiveDropdown(null);
  };

  return (
    <header className="header">
      <div className="header-container">
        <div className="logo">
          <img src={logo} alt="LeadCRM Logo" />
        </div>

        {/* Desktop Navigation */}
        <nav className={`nav-links ${isMenuOpen ? 'nav-open' : ''}`}>
          <div className="dropdown">
            <button
              className="dropbtn"
              onClick={() => toggleDropdown('product')}
              onMouseEnter={() => window.innerWidth > 768 && setActiveDropdown('product')}
              onMouseLeave={() => window.innerWidth > 768 && setActiveDropdown(null)}
            >
              Product ▼
            </button>
            <div className={`dropdown-content ${activeDropdown === 'product' ? 'show' : ''}`}>
              <a href="#" onClick={closeMenu}>Features</a>
              <a href="#" onClick={closeMenu}>Integrations</a>
              <a href="#" onClick={closeMenu}>API</a>
            </div>
          </div>

          <a href="#" onClick={closeMenu}>Pricing</a>

          <div className="dropdown">
            <button
              className="dropbtn"
              onClick={() => toggleDropdown('resources')}
              onMouseEnter={() => window.innerWidth > 768 && setActiveDropdown('resources')}
              onMouseLeave={() => window.innerWidth > 768 && setActiveDropdown(null)}
            >
              Resources ▼
            </button>
            <div className={`dropdown-content ${activeDropdown === 'resources' ? 'show' : ''}`}>
              <a href="#" onClick={closeMenu}>Blog</a>
              <a href="#" onClick={closeMenu}>Docs</a>
              <a href="#" onClick={closeMenu}>Help Center</a>
            </div>
          </div>

          <div className="dropdown">
            <button
              className="dropbtn"
              onClick={() => toggleDropdown('company')}
              onMouseEnter={() => window.innerWidth > 768 && setActiveDropdown('company')}
              onMouseLeave={() => window.innerWidth > 768 && setActiveDropdown(null)}
            >
              Company ▼
            </button>
            <div className={`dropdown-content ${activeDropdown === 'company' ? 'show' : ''}`}>
              <a href="#" onClick={closeMenu}>About Us</a>
              <a href="#" onClick={closeMenu}>Careers</a>
              <a href="#" onClick={closeMenu}>Contact</a>
            </div>
          </div>

          {/* Mobile Auth Buttons */}
          <div className="mobile-auth-buttons">
            <button className="free-account" onClick={closeMenu}>Get your free account</button>
            <button className="login" onClick={closeMenu}>Login</button>
          </div>
        </nav>

        {/* Desktop Auth Buttons */}
        <div className="auth-buttons desktop-auth">
          <button className="free-account">Get your free account</button>
          <button className="login">Login</button>
        </div>

        {/* Mobile Menu Toggle */}
        <button
          className={`menu-toggle ${isMenuOpen ? 'active' : ''}`}
          onClick={toggleMenu}
          aria-label="Toggle menu"
        >
          <span className="hamburger-line"></span>
          <span className="hamburger-line"></span>
          <span className="hamburger-line"></span>
        </button>
      </div>

      {/* Mobile Menu Overlay */}
      {isMenuOpen && <div className="menu-overlay" onClick={closeMenu}></div>}
    </header>
  );
};

export default Header;
