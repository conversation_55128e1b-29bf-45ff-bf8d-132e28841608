import React from 'react';
import './Footer.css';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="footer">
      <div className="container">
        <div className="footer__content">
          <div className="footer__brand">
            <div className="footer__logo">
              <div className="logo">
                <div className="logo__icon">
                  <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                    <circle cx="16" cy="16" r="16" fill="#0066cc"/>
                    <path d="M12 10h8v2h-8v-2zm0 4h8v2h-8v-2zm0 4h6v2h-6v-2z" fill="white"/>
                  </svg>
                </div>
                <span className="logo__text">LeadCRM</span>
              </div>
            </div>
            <p className="footer__description">
              The most powerful LinkedIn CRM integration platform. 
              Transform your LinkedIn network into a sales powerhouse.
            </p>
            <div className="footer__social">
              <a href="#" className="social-link" aria-label="LinkedIn">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z"/>
                </svg>
              </a>
              <a href="#" className="social-link" aria-label="Twitter">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84"/>
                </svg>
              </a>
              <a href="#" className="social-link" aria-label="Facebook">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M20 10c0-5.523-4.477-10-10-10S0 4.477 0 10c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V10h2.54V7.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V10h2.773l-.443 2.89h-2.33v6.988C16.343 19.128 20 14.991 20 10z"/>
                </svg>
              </a>
            </div>
          </div>

          <div className="footer__links">
            <div className="footer__column">
              <h4 className="footer__column-title">Product</h4>
              <ul className="footer__list">
                <li><a href="#features" className="footer__link">Features</a></li>
                <li><a href="#integrations" className="footer__link">Integrations</a></li>
                <li><a href="#pricing" className="footer__link">Pricing</a></li>
                <li><a href="#api" className="footer__link">API</a></li>
                <li><a href="#security" className="footer__link">Security</a></li>
              </ul>
            </div>

            <div className="footer__column">
              <h4 className="footer__column-title">Company</h4>
              <ul className="footer__list">
                <li><a href="#about" className="footer__link">About Us</a></li>
                <li><a href="#careers" className="footer__link">Careers</a></li>
                <li><a href="#blog" className="footer__link">Blog</a></li>
                <li><a href="#press" className="footer__link">Press</a></li>
                <li><a href="#contact" className="footer__link">Contact</a></li>
              </ul>
            </div>

            <div className="footer__column">
              <h4 className="footer__column-title">Resources</h4>
              <ul className="footer__list">
                <li><a href="#help" className="footer__link">Help Center</a></li>
                <li><a href="#docs" className="footer__link">Documentation</a></li>
                <li><a href="#guides" className="footer__link">Guides</a></li>
                <li><a href="#webinars" className="footer__link">Webinars</a></li>
                <li><a href="#status" className="footer__link">Status</a></li>
              </ul>
            </div>

            <div className="footer__column">
              <h4 className="footer__column-title">Legal</h4>
              <ul className="footer__list">
                <li><a href="#privacy" className="footer__link">Privacy Policy</a></li>
                <li><a href="#terms" className="footer__link">Terms of Service</a></li>
                <li><a href="#cookies" className="footer__link">Cookie Policy</a></li>
                <li><a href="#gdpr" className="footer__link">GDPR</a></li>
                <li><a href="#compliance" className="footer__link">Compliance</a></li>
              </ul>
            </div>
          </div>
        </div>

        <div className="footer__bottom">
          <div className="footer__copyright">
            <p>&copy; {currentYear} LeadCRM. All rights reserved.</p>
          </div>
          <div className="footer__badges">
            <div className="badge">
              <span className="badge__icon">🔒</span>
              <span>SOC 2 Certified</span>
            </div>
            <div className="badge">
              <span className="badge__icon">🛡️</span>
              <span>GDPR Compliant</span>
            </div>
            <div className="badge">
              <span className="badge__icon">⭐</span>
              <span>99.9% Uptime</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
