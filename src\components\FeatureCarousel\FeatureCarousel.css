/* .carousel-container {
  width: 100%;
  padding: 40px 20px;
  background: #fff;
}

.carousel-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
}

.carousel-item .icon {
  font-size: 36px;
  color: #0a66c2;
  margin-bottom: 12px;
}

.carousel-item p {
  font-size: 16px;
  font-weight: 500;
  color: #333;
} */


/* .carousel-wrapper {
  width: 100%;
  overflow: hidden;
  position: relative;
}

.carousel {
  display: flex;
  gap: 20px;
  padding: 15px;
  white-space: nowrap;
}

.carousel-item {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  font-weight: 500;
  color: #1a1a1a;
  border-radius: 8px;
  padding: 10px 15px;
  background: #fff;
  box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
  cursor: pointer;
  min-width: 220px; 
}

.carousel-item:hover {
  transform: translateY(-4px);
}

.icon {
  font-size: 20px;
}

@media (max-width: 768px) {
  .carousel-item {
    min-width: 180px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .carousel-item {
    min-width: 150px;
    font-size: 12px;
    padding: 8px 12px;
  }
} */


/* Wrapper */
.fc {
  width: 100%;
  overflow: hidden;
  position: relative;
  padding: 10px 0;
  background: #f9f9f9; /* optional */
}

.fc-track {
  display: flex;
  align-items: center;
  gap: clamp(14px, 3vw, 28px);
  width: max-content;
  animation: scroll-left 25s linear infinite;
}

.fc:hover .fc-track {
  animation-play-state: paused;
}

.fc-item {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 14px;
  font-weight: 500;
  font-size: clamp(12px, 1.05vw, 14px);
  min-width: clamp(160px, 34vw, 280px);
  white-space: nowrap;
}

.fc-icon {
  font-size: 20px;
  line-height: 1;
  width: 24px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fc-icon img {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

/* Animation: infinite right → left */
@keyframes scroll-left {
  from { transform: translateX(0); }
  to   { transform: translateX(-50%); }
}

@media (prefers-reduced-motion: reduce) {
  .fc-track { animation: none; }
}
