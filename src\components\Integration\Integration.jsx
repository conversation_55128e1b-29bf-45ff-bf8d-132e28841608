import React from 'react';
import './Integration.css';
import integration1 from '../../assets/salesforce.png';
import integration2 from '../../assets/hubspot.png';
import integration3 from '../../assets/pipedrive.png';
import integration4 from '../../assets/captera.png';
import integration5 from '../../assets/toprated.png';


const Integration = () => {
  return (
    <section className="hero-section">
      <div className="hero-container">
        {/* Top Tagline */}
        <div className="hero-tagline">
          <p>Turn Your LinkedIn into a Revenue Engine.</p>
        </div>

        {/* Main Hero Content */}
        <div className="hero-content">
          <div className="hero-banner">
            <div className="banner-header">
              <h1 className="banner-title">
                Your LinkedIn Powerhouse for Smarter Lead Management
              </h1>
            </div>

            {/* Integration Badges */}
            <div className="integration-badges">
              <div className="badge salesforce">
                <img src={integration1} alt="Salesforce" className="badge-logo" />
              </div>
              <div className="badge hubspot">
                <img src={integration2} alt="HubSpot" className="badge-logo" />
              </div>
              <div className="badge pipedrive">
                <img src={integration3} alt="Pipedrive" className="badge-logo" />
              </div>
              <div className="badge copper">
                <img src={integration4} alt="Copper CRM" className="badge-logo" />
              </div>
              <div className="badge zoho">
                <img src={integration5} alt="Zoho" className="badge-logo" />
              </div>
            </div>

            {/* Dashboard Screenshots */}
            <div className="dashboard-showcase">
              <div className="dashboard-item dashboard-1">
                <div className="dashboard-window">
                  <div className="window-header">
                    <div className="window-controls">
                      <span className="control red"></span>
                      <span className="control yellow"></span>
                      <span className="control green"></span>
                    </div>
                  </div>
                  <div className="window-content">
                    <div className="linkedin-profile">
                      <div className="profile-header">
                        <div className="profile-avatar"></div>
                        <div className="profile-info">
                          <div className="profile-name"></div>
                          <div className="profile-title"></div>
                        </div>
                      </div>
                      <div className="profile-actions">
                        <div className="action-btn primary">Connect</div>
                        <div className="action-btn secondary">Message</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="dashboard-item dashboard-2">
                <div className="dashboard-window">
                  <div className="window-header">
                    <div className="window-controls">
                      <span className="control red"></span>
                      <span className="control yellow"></span>
                      <span className="control green"></span>
                    </div>
                  </div>
                  <div className="window-content">
                    <div className="crm-interface">
                      <div className="crm-header">
                        <div className="crm-title">Lead Management</div>
                      </div>
                      <div className="crm-list">
                        <div className="crm-item">
                          <div className="item-avatar"></div>
                          <div className="item-info">
                            <div className="item-name"></div>
                            <div className="item-company"></div>
                          </div>
                          <div className="item-status active"></div>
                        </div>
                        <div className="crm-item">
                          <div className="item-avatar"></div>
                          <div className="item-info">
                            <div className="item-name"></div>
                            <div className="item-company"></div>
                          </div>
                          <div className="item-status pending"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="dashboard-item dashboard-3">
                <div className="dashboard-window">
                  <div className="window-header">
                    <div className="window-controls">
                      <span className="control red"></span>
                      <span className="control yellow"></span>
                      <span className="control green"></span>
                    </div>
                  </div>
                  <div className="window-content">
                    <div className="sync-interface">
                      <div className="sync-header">Data Sync</div>
                      <div className="sync-progress">
                        <div className="progress-bar">
                          <div className="progress-fill"></div>
                        </div>
                        <div className="sync-status">Syncing LinkedIn data...</div>
                      </div>
                      <div className="sync-items">
                        <div className="sync-item completed">
                          <div className="sync-icon"></div>
                          <span>Profile Information</span>
                        </div>
                        <div className="sync-item completed">
                          <div className="sync-icon"></div>
                          <span>Contact Details</span>
                        </div>
                        <div className="sync-item active">
                          <div className="sync-icon"></div>
                          <span>Experience Data</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="dashboard-item dashboard-4">
                <div className="dashboard-window">
                  <div className="window-header">
                    <div className="window-controls">
                      <span className="control red"></span>
                      <span className="control yellow"></span>
                      <span className="control green"></span>
                    </div>
                  </div>
                  <div className="window-content">
                    <div className="analytics-interface">
                      <div className="analytics-header">Analytics Dashboard</div>
                      <div className="analytics-stats">
                        <div className="stat-item">
                          <div className="stat-number">247</div>
                          <div className="stat-label">Leads Generated</div>
                        </div>
                        <div className="stat-item">
                          <div className="stat-number">89%</div>
                          <div className="stat-label">Sync Success Rate</div>
                        </div>
                      </div>
                      <div className="analytics-chart">
                        <div className="chart-bars">
                          <div className="chart-bar" style={{height: '60%'}}></div>
                          <div className="chart-bar" style={{height: '80%'}}></div>
                          <div className="chart-bar" style={{height: '45%'}}></div>
                          <div className="chart-bar" style={{height: '90%'}}></div>
                          <div className="chart-bar" style={{height: '70%'}}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="dashboard-item dashboard-5">
                <div className="dashboard-window">
                  <div className="window-header">
                    <div className="window-controls">
                      <span className="control red"></span>
                      <span className="control yellow"></span>
                      <span className="control green"></span>
                    </div>
                  </div>
                  <div className="window-content">
                    <div className="notification-interface">
                      <div className="notification-header">Recent Activity</div>
                      <div className="notification-list">
                        <div className="notification-item">
                          <div className="notification-icon success"></div>
                          <div className="notification-text">
                            <div className="notification-title">Lead Added</div>
                            <div className="notification-desc">John Doe synced to CRM</div>
                          </div>
                        </div>
                        <div className="notification-item">
                          <div className="notification-icon info"></div>
                          <div className="notification-text">
                            <div className="notification-title">Data Updated</div>
                            <div className="notification-desc">Contact information refreshed</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="hero-cta">
          <button className="cta-button">
            <span>Get Started Today</span>
            <svg className="cta-arrow" width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M5 12h14M12 5l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
        </div>
      </div>

      {/* Background Elements */}
      <div className="hero-background">
        <div className="bg-gradient"></div>
        <div className="bg-pattern"></div>
      </div>
    </section>
  );
};

export default Integration;
