import React from "react";
import "./FeatureCarousel.css";

// Image import
import carousel1 from "../../assets/carousel1.png";
import carousel2 from "../../assets/carousel2.png";
import carousel3 from "../../assets/carousel3.png";
import carousel4 from "../../assets/carousel4.png";

const features = [
  { icon: carousel1, title: "Access to 700M+ Contacts" },  
  { icon: carousel2, title: "One click push to CRM" }, 
  { icon: carousel3, title: "Custom Field Mapping" }, 
  { icon: carousel4, title: "Advanced Waterfall Enrichment" },
];

// Duplicate list for infinite loop
const loopItems = [...features, ...features];

function FeatureCarousel() {
  return (
    <div className="fc">
      <div className="fc-track">
        {loopItems.map((f, i) => (
          <div className="fc-item" key={i}>
            <div className="fc-icon">
              {typeof f.icon === "string" && f.icon.includes("/") ? (
                <img src={f.icon} alt="feature" />
              ) : (
                <span>{f.icon}</span>
              )}
            </div>
            <span className="fc-text">{f.title}</span>
          </div>
        ))}
      </div>
    </div>
  );
}

export default FeatureCarousel;

