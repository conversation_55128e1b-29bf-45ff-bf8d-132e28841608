import React from "react";
import "./Comparison.css";
import without1 from '../../assets/without1.png';
import without2 from '../../assets/without2.png';
import cross from '../../assets/Cross.png';
import icon1 from '../../assets/with1.png';
import icon2 from '../../assets/with2.png';
import icon3 from '../../assets/with3.png';
import icon4 from '../../assets/with4.png';


const Comparison = () => {
  return (
    <section className="comparison-section">
      <h2>Stop Letting a Broken Workflow Dictate Your Sales Results.</h2>

      <div className="comparison-cards">
        <div className="card without-leadcrm">
          <div className="card-image">
            <img src={without1} alt="Without LeadCRM Workflow" />
          </div>
          <h3>Without LeadCRM</h3>
          <ul>
            <li>
              <img src={cross} alt="Cross Icon" className="icon" />
              <div>
                <strong>Manual Data Entry</strong>
                <p>Copying LinkedIn contacts to CRM manually plus losing conversation history</p>
              </div>
            </li>
            <li>
              <img src={cross} alt="Cross Icon" className="icon" />
              <div>
                <strong>Incomplete Data</strong>
                <p>LinkedIn profiles missing Email and Phones from 700M+ Database</p>
              </div>
            </li>
            <li>
              <img src={cross} alt="Cross Icon" className="icon" />
              <div>
                <strong>No CRM Visibility</strong>
                <p>Can't see existing CRM contacts when browsing LinkedIn profiles</p>
              </div>
            </li>
            <li>
              <img src={cross} alt="Cross Icon" className="icon" />
              <div>
                <strong>Limited Productivity</strong>
                <p>Writing messages manually plus no AI assistant</p>
              </div>
            </li>
          </ul>
        </div>

        <div className="vs-divider">VS</div>

        <div className="card with-leadcrm">
          <div className="card-image">
            <img src={without2} alt="With LeadCRM Workflow" />
          </div>
          <h3>With LeadCRM</h3>
          <ul>
            <li>
              <img src={icon1} alt="Check Icon" className="icon" />
              <div>
                <strong>Complete Bi-Directional Sync</strong>
                <p>Automatically sync contacts, messages, and notes in real time</p>
              </div>
            </li>
            <li>
              <img src={icon2} alt="Check Icon" className="icon" />
              <div>
                <strong>700M+ Contacts + Enrichment</strong>
                <p>Get verified emails and phone numbers from a vast global database</p>
              </div>
            </li>
            <li>
              <img src={icon3} alt="Check Icon" className="icon" />
              <div>
                <strong>CRM Overlay on LinkedIn</strong>
                <p>See full CRM insights directly on LinkedIn profiles without switching tabs</p>
              </div>
            </li>
            <li>
              <img src={icon4} alt="Check Icon" className="icon" />
              <div>
                <strong>AI Response + Templates + Bulk Exports</strong>
                <p>Save time with AI-crafted replies, pre-built templates, and one-click exports</p>
              </div>
            </li>
          </ul>
        </div>
      </div>

      <button className="cta-button">Get a Free Account Now!</button>

      <p className="small-note">Save 40+ hours every Month</p>
    </section>
  );
};

export default Comparison;
