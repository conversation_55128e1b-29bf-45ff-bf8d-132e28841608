import React from "react";
import "./HowItWorks.css";
import work1 from '../../assets/works1.png';
import work2 from '../../assets/works2.png';
import work3 from '../../assets/works3.png';
import work4 from '../../assets/works4.png';


const HowItWorks = () => {
  return (
    <section className="how-it-works">
      <h2>How it Works</h2>
      <p>From setup to success in <strong>4 simple steps</strong></p>

      <div className="steps-container">
        <div className="step">
          <div className="step-number">1</div>
          <h3>Install the Extension</h3>
          <p>Add LeadCRM to Chrome and connect your CRM in 2 minutes – secure and seamless.</p>
          <img src={work1} alt="Install the Extension" />
        </div>

        <div className="step">
          <div className="step-number">2</div>
          <h3>Browse LinkedIn</h3>
          <p>Use LinkedIn normally, our auto-captures data and reveals CRM contacts instantly.</p>
          <img src={work2} alt="Browse LinkedIn" />
        </div>

        <div className="step">
          <div className="step-number">3</div>
          <h3>Get Enriched Data</h3>
          <p>Enrich profiles with verified emails, phones, and company data with 95%+ accuracy guaranteed.</p>
          <img src={work3} alt="Enriched Data" />
        </div>

        <div className="step">
          <div className="step-number">4</div>
          <h3>Sync to CRM Instantly</h3>
          <p>Prospect data syncs to your CRM instantly with history, tracking, and AI-powered insights.</p>
          <img src={work4} alt="Sync to CRM" />
        </div>
      </div>

      <button className="try-now">Try LeadCRM Now →</button>
    </section>
  );
};

export default HowItWorks;
