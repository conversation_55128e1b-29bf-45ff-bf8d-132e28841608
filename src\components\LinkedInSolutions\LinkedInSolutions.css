.linkedin-container {
  max-width: 1200px;
  margin: auto;
  padding: 40px 20px;
  font-family: 'Segoe UI', sans-serif;
  text-align: center;
}

.main-heading {
  font-size: 28px;
  font-weight: bold;
  color: #002244;
}

.sub-heading {
  font-size: 16px;
  margin-bottom: 20px;
  color: #555;
}

/* Feature Tabs */
.features-tabs {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 30px 0 20px;
  border-bottom: 2px solid #eee;
  gap: 40px;
  flex-wrap: wrap;
  padding-bottom: 10px;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 14px;
  color: #333;
  font-weight: 500;
  cursor: pointer;
  position: relative;
}

.feature-item img {
  width: 30px;
  height: 30px;
  margin-bottom: 5px;
}

.feature-item.active span {
  color: #0d1c55;
  font-weight: bold;
}

.feature-item.active::after {
  content: '';
  width: 100%;
  height: 3px;
  background-color: #0d1c55;
  position: absolute;
  bottom: -12px;
  left: 0;
}

/* Bot Icon */
.image-title-section .bot-icon {
  width: 50px;
  margin: 20px auto;
}

/* Incomplete Data Text */
.info-text {
  font-size: 15px;
  margin: 20px 0;
}

.incomplete-data {
  background-color: #ffdada;
  padding: 2px 6px;
  border-radius: 4px;
  color: #c00;
  font-weight: bold;
}

/* Boxes */
.boxes-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 30px;
  margin-top: 30px;
}

.box {
  flex: 1 1 45%;
  background-color: #f5f8ff;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0px 0px 8px rgba(0,0,0,0.05);
  text-align: left;
}

.box-subheading {
  font-size: 14px;
  color: #555;
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 10px;
}

.green-link {
  color: #00aa63;
  font-weight: bold;
  margin-top: 10px;
}

.green-text {
  color: #00aa63;
  font-weight: bold;
}

/* Images inside box */
.images {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 20px;
}

.two-images {
  flex-direction: row;
  justify-content: space-between;
}

.two-images .box-image {
  width: 48%;
  object-fit: contain;
}

.box-image {
  width: 100%;
  border-radius: 8px;
  object-fit: contain;
}

.waterfall-image img {
  width: 100%;
  margin-top: 20px;
  object-fit: contain;
}

/* Responsive Design */
@media (max-width: 768px) {
  .boxes-container {
    flex-direction: column;
  }

  .box {
    flex: 1 1 100%;
  }

  .features-tabs {
    gap: 20px;
  }

  .two-images {
    flex-direction: column;
  }

  .two-images .box-image {
    width: 100%;
  }
}
