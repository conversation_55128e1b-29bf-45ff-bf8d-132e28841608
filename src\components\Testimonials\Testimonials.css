/* Testimonials Component Styles */
.testimonials-section {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 5rem 0;
  position: relative;
  overflow: hidden;
}

.testimonials-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
}

/* Header */
.testimonials-header {
  text-align: center;
  margin-bottom: 3rem;
}

.testimonials-title {
  font-size: 2.25rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  line-height: 1.2;
}

/* Testimonials Grid */
.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  align-items: stretch;
}

.testimonial-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  border: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  min-height: 320px;
}

.testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.testimonial-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  height: 100%;
  flex: 1;
}

/* Testimonial Text */
.testimonial-text {
  flex: 1;
  display: flex;
  align-items: flex-start;
  min-height: 120px;
}

.testimonial-text p {
  font-size: 0.95rem;
  line-height: 1.6;
  color: #475569;
  margin: 0;
  font-style: italic;
  display: -webkit-box;
  -webkit-line-clamp: 6;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Rating Stars */
.testimonial-rating {
  display: flex;
  justify-content: flex-start;
}

.stars {
  display: flex;
  gap: 0.25rem;
}

.star {
  font-size: 1.1rem;
  transition: color 0.2s ease;
}

.star.filled {
  color: #fbbf24;
}

.star.empty {
  color: #e5e7eb;
}

/* Author Section */
.testimonial-author {
  border-top: 1px solid #f1f5f9;
  padding-top: 1.5rem;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.author-avatar {
  position: relative;
  flex-shrink: 0;
}

.avatar-img {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e2e8f0;
}

.verified-badge {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 20px;
  height: 20px;
  background: #10b981;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  border: 2px solid white;
}

.verified-badge svg {
  width: 12px;
  height: 12px;
}

.author-details {
  flex: 1;
}

.author-name {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
}

.author-role {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
}

/* Background Decorations */
.testimonials-decorations {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.1;
}

.circle-1 {
  width: 200px;
  height: 200px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  top: 10%;
  right: 5%;
  animation: float 6s ease-in-out infinite;
}

.circle-2 {
  width: 150px;
  height: 150px;
  background: linear-gradient(135deg, #10b981, #059669);
  bottom: 20%;
  left: 8%;
  animation: float 8s ease-in-out infinite reverse;
}

.circle-3 {
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  top: 60%;
  right: 15%;
  animation: float 7s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .testimonials-section {
    padding: 4rem 0;
  }

  .testimonials-container {
    padding: 0 1.5rem;
  }

  .testimonials-title {
    font-size: 2rem;
  }

  .testimonials-grid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1.5rem;
  }

  .testimonial-card {
    padding: 1.5rem;
    min-height: 300px;
  }

  .testimonial-text {
    min-height: 100px;
  }
}

@media (max-width: 768px) {
  .testimonials-section {
    padding: 3rem 0;
  }

  .testimonials-container {
    padding: 0 1rem;
  }

  .testimonials-header {
    margin-bottom: 2rem;
  }

  .testimonials-title {
    font-size: 1.75rem;
  }

  .testimonials-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .testimonial-card {
    padding: 1.25rem;
    min-height: 280px;
  }

  .testimonial-text {
    min-height: 90px;
  }

  .testimonial-text p {
    font-size: 0.9rem;
    -webkit-line-clamp: 5;
  }

  .author-info {
    gap: 0.75rem;
  }

  .avatar-img {
    width: 42px;
    height: 42px;
  }

  .verified-badge {
    width: 18px;
    height: 18px;
  }

  .verified-badge svg {
    width: 10px;
    height: 10px;
  }

  .author-name {
    font-size: 0.9rem;
  }

  .author-role {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .testimonials-section {
    padding: 2.5rem 0;
  }

  .testimonials-container {
    padding: 0 0.75rem;
  }

  .testimonials-title {
    font-size: 1.5rem;
  }

  .testimonial-card {
    padding: 1rem;
    border-radius: 12px;
    min-height: 260px;
  }

  .testimonial-content {
    gap: 1.25rem;
  }

  .testimonial-text {
    min-height: 80px;
  }

  .testimonial-text p {
    font-size: 0.85rem;
    line-height: 1.5;
    -webkit-line-clamp: 4;
  }

  .stars {
    gap: 0.2rem;
  }

  .star {
    font-size: 1rem;
  }

  .testimonial-author {
    padding-top: 1.25rem;
    margin-top: auto;
  }

  .author-info {
    gap: 0.6rem;
  }

  .avatar-img {
    width: 38px;
    height: 38px;
  }

  .verified-badge {
    width: 16px;
    height: 16px;
    bottom: -1px;
    right: -1px;
  }

  .verified-badge svg {
    width: 9px;
    height: 9px;
  }

  .author-name {
    font-size: 0.85rem;
  }

  .author-role {
    font-size: 0.75rem;
  }

  /* Hide decorative circles on small screens */
  .decoration-circle {
    display: none;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .testimonial-card,
  .decoration-circle {
    transition: none;
    animation: none;
  }
}

/* Focus states */
.testimonial-card:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .testimonial-card {
    border: 2px solid #1e293b;
  }
  
  .star.filled {
    color: #f59e0b;
  }
  
  .verified-badge {
    background: #059669;
  }
}
