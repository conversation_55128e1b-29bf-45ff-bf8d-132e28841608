import React from 'react';
import './CrmIntegration.css';
import crmFlowImage from '../../assets/Crmintegration.jpeg'; // ← Replace this with your actual image path

const CrmIntegration = () => {
  return (
    <section className="crm-section">
      <div className="crm-content">
        <h2 className="crm-heading">Your CRM, Now the Single Source of Truth.</h2>
        <p className="crm-subheading">
          Sync complete LinkedIn profiles and use our flexible field mapping to ensure every detail,
          from job history to contact info, populates exactly where you need it,
          creating a perfectly organized CRM automatically.
        </p>
        <div className="crm-image-container">
          <img src={crmFlowImage} alt="CRM Flow" className="crm-flow-img" />
        </div>
        <div className="crm-cta">
          <button className="crm-btn">Try LeadCRM Now</button>
        </div>
      </div>
    </section>
  );
};

export default CrmIntegration;
